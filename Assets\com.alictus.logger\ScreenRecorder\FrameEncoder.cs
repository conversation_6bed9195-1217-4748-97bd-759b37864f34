using System;
using System.Collections.Generic;
using Unity.Collections; // For NativeArray
using UnityEngine; // For Debug.LogWarning if any issues arise

namespace ScreenRecording
{
    public static class FrameEncoder
    {
        public static EncodedFrame EncodeFrame(
            RawFrameData currentRawFrame,
            RawFrameData previousRawFrame, // Can be null for the very first frame
            int blockSize,
            int colorQuantizationFactor)
        {
            if (currentRawFrame == null) throw new ArgumentNullException(nameof(currentRawFrame));
            if (blockSize <= 0) throw new ArgumentOutOfRangeException(nameof(blockSize), "Block size must be positive.");
            if (colorQuantizationFactor <= 0) throw new ArgumentOutOfRangeException(nameof(colorQuantizationFactor), "Color quantization factor must be positive.");

            string frameSessionId = PerformanceProfiler.StartSession("Frame_Encode",
                $"Frame {currentRawFrame.FrameNumber}, Size: {currentRawFrame.Width}x{currentRawFrame.Height}");

            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();

            int width = currentRawFrame.Width;
            int height = currentRawFrame.Height;

            var (numBlocksX, numBlocksY) = PerformanceProfiler.TimeOperation("Calculate_Block_Dimensions", () => {
                EncodedFrame.CalculateBlockDimensions(width, height, blockSize, out int x, out int y);
                return (x, y);
            }, $"Size: {width}x{height}, BlockSize: {blockSize}");

            byte[] matchBitArray = new byte[(numBlocksX * numBlocksY + 7) / 8]; // Auto-initialized to zeros
            List<byte> nonMatchingDataList = new List<byte>(); // To store differing block data

            // Handle the first frame (reference frame) or if previous frame is incompatible
            bool isReferenceFrame = previousRawFrame == null ||
                                    previousRawFrame.Width != width ||
                                    previousRawFrame.Height != height;

            if (isReferenceFrame)
            {
                PerformanceProfiler.TimeOperation("Reference_Frame_Processing", () => {
                    // All blocks are "non-matching" for the first/reference frame.
                    // MatchBitArray is already all zeros.
                    for (int blockY = 0; blockY < numBlocksY; blockY++)
                    {
                        for (int blockX = 0; blockX < numBlocksX; blockX++)
                        {
                            AppendBlockData(currentRawFrame.PixelData, width, height,
                                            blockX, blockY, blockSize, colorQuantizationFactor,
                                            nonMatchingDataList);
                        }
                    }
                }, $"Blocks: {numBlocksX}x{numBlocksY} = {numBlocksX * numBlocksY}");
            }
            else // Perform diffing against the previous frame
            {
                var (matchingBlocks, nonMatchingBlocks) = PerformanceProfiler.TimeOperation("Frame_Diffing", () => {
                    int bitIndex = 0;
                    int matching = 0;
                    int nonMatching = 0;

                    for (int blockY = 0; blockY < numBlocksY; blockY++)
                    {
                        for (int blockX = 0; blockX < numBlocksX; blockX++)
                        {
                            bool matches = CompareBlocks(
                                                currentRawFrame.PixelData, previousRawFrame.PixelData,
                                                width, height, blockX, blockY, blockSize,
                                                colorQuantizationFactor);

                            if (matches)
                            {
                                SetBit(matchBitArray, bitIndex);
                                matching++;
                            }
                            else
                            {
                                // Bit is already 0 (no match) in matchBitArray
                                AppendBlockData(currentRawFrame.PixelData, width, height,
                                                blockX, blockY, blockSize, colorQuantizationFactor,
                                                nonMatchingDataList);
                                nonMatching++;
                            }
                            bitIndex++;
                        }
                    }
                    return (matching, nonMatching);
                }, $"Blocks: {numBlocksX}x{numBlocksY} = {numBlocksX * numBlocksY}");

                UnityEngine.Debug.Log($"[FrameEncoder] Frame {currentRawFrame.FrameNumber}: {matchingBlocks} matching, {nonMatchingBlocks} non-matching blocks");
            }

            stopwatch.Stop();

            var encodedFrame = new EncodedFrame
            {
                FrameNumber = currentRawFrame.FrameNumber,
                SourceWidth = width,
                SourceHeight = height,
                BlockSize = blockSize,
                ColorQuantizationFactor = colorQuantizationFactor,
                NumBlocksX = numBlocksX,
                NumBlocksY = numBlocksY,
                MatchBitArray = matchBitArray,
                NonMatchingBlockData = nonMatchingDataList.ToArray(),
                EncodingDurationMs = (float)stopwatch.Elapsed.TotalMilliseconds
            };

            PerformanceProfiler.EndSession(frameSessionId,
                $"Success - {encodedFrame.NonMatchingBlockData.Length} bytes, {encodedFrame.EncodingDurationMs:F2}ms");

            return encodedFrame;
        }

        private static void AppendBlockData(
            NativeArray<byte> sourcePixelData, int sourceWidth, int sourceHeight,
            int blockX, int blockY, int blockSize, int quantizationFactor,
            List<byte> outputData)
        {
            int startPixelX = blockX * blockSize;
            int startPixelY = blockY * blockSize;

            for (int yInBlock = 0; yInBlock < blockSize; yInBlock++)
            {
                int currentPixelY = startPixelY + yInBlock;
                if (currentPixelY >= sourceHeight) continue; // Past bottom edge

                for (int xInBlock = 0; xInBlock < blockSize; xInBlock++)
                {
                    int currentPixelX = startPixelX + xInBlock;
                    if (currentPixelX >= sourceWidth) continue; // Past right edge

                    int pixelDataIndex = (currentPixelY * sourceWidth + currentPixelX) * 4;

                    byte bRaw = sourcePixelData[pixelDataIndex + 0]; // B
                    byte gRaw = sourcePixelData[pixelDataIndex + 1]; // G
                    byte rRaw = sourcePixelData[pixelDataIndex + 2]; // R
                    // Alpha (pixelDataIndex + 3) is ignored.
 
                    byte r = rRaw;
                    byte g = gRaw;
                    byte b = bRaw;

                    if (quantizationFactor > 1)
                    {
                        r = (byte)(r / quantizationFactor);
                        g = (byte)(g / quantizationFactor);
                        b = (byte)(b / quantizationFactor);
                    }
                    outputData.Add(r);
                    outputData.Add(g);
                    outputData.Add(b);
                }
            }
        }

        private static bool CompareBlocks(
            NativeArray<byte> currentFramePixels, NativeArray<byte> previousFramePixels,
            int width, int height, int blockX, int blockY, int blockSize,
            int quantizationFactor)
        {
            // Note: Adding detailed timing here would be too granular and impact performance
            // The timing is handled at the calling level in Frame_Diffing operation

            int startPixelX = blockX * blockSize;
            int startPixelY = blockY * blockSize;

            for (int yInBlock = 0; yInBlock < blockSize; yInBlock++)
            {
                int currentPixelY = startPixelY + yInBlock;
                if (currentPixelY >= height) continue;

                for (int xInBlock = 0; xInBlock < blockSize; xInBlock++)
                {
                    int currentPixelX = startPixelX + xInBlock;
                    if (currentPixelX >= width) continue;

                    int pixelDataIndex = (currentPixelY * width + currentPixelX) * 4;

                    // Assuming R,G,B,A byte order from NativeArray as in AppendBlockData.
                    // Adjust if actual byte order is different (e.g., BGRA).
                    byte currentB = currentFramePixels[pixelDataIndex + 0]; // B
                    byte currentG = currentFramePixels[pixelDataIndex + 1]; // G
                    byte currentR = currentFramePixels[pixelDataIndex + 2]; // R

                    byte prevB = previousFramePixels[pixelDataIndex + 0]; // B
                    byte prevG = previousFramePixels[pixelDataIndex + 1]; // G
                    byte prevR = previousFramePixels[pixelDataIndex + 2]; // R

                    if (quantizationFactor > 1)
                    {
                        currentR = (byte)(currentR / quantizationFactor);
                        currentG = (byte)(currentG / quantizationFactor);
                        currentB = (byte)(currentB / quantizationFactor);

                        prevR = (byte)(prevR / quantizationFactor);
                        prevG = (byte)(prevG / quantizationFactor);
                        prevB = (byte)(prevB / quantizationFactor);
                    }

                    if (currentR != prevR || currentG != prevG || currentB != prevB) // Logic remains R,G,B comparison
                    {
                        return false; // Mismatch found
                    }
                }
            }
            return true; // All pixels in the block match after quantization
        }

        private static void SetBit(byte[] byteArray, int bitIndex)
        {
            if (byteArray == null) throw new ArgumentNullException(nameof(byteArray));
            int byteIndex = bitIndex / 8;
            if (byteIndex < 0 || byteIndex >= byteArray.Length) throw new ArgumentOutOfRangeException(nameof(bitIndex), "Bit index is out of bounds for the byte array.");
            
            int bitInByte = bitIndex % 8;
            byteArray[byteIndex] |= (byte)(1 << bitInByte);
        }
    }
}