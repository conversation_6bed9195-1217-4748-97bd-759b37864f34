using Cysharp.Threading.Tasks;
using com.alictus.logger.Common;
using com.alictus.logger.runtime;

namespace com.alictus.logger.Interfaces
{
    /// <summary>
    /// Responsible for forwarding a <see cref="SessionData"/> payload to the backend (or other
    /// consumer) according to the configuration supplied at construction time.
    /// </summary>
    public interface ISessionEventSender
    {
        UniTask SendAsync(SessionData sessionData);
    }
}
