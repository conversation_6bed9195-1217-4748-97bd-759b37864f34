namespace com.alictus.common
{
    public static class KeyChainProvider
    {
        public static string GetValueFromKey(string key)
        {
#if UNITY_IOS && !UNITY_EDITOR
            return KeyChain.GetKeyChainValue(key);
#endif
            return "";
        }

        public static void SetValueToKey(string key, string value)
        {
#if UNITY_IOS && !UNITY_EDITOR
            KeyChain.SetKeyChainValue(key, value);;
#endif

        }

        public static void RemoveKeyValue(string key)
        {
#if UNITY_IOS && !UNITY_EDITOR
            KeyChain.RemoveKeyChainValue(key);
#endif
        }
    }
}