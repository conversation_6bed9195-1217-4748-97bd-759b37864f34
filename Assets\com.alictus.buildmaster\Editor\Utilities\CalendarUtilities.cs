using System;
using UnityEngine;

namespace com.alictus.buildmaster.Editor.Utilities
{
    public static class CalendarUtilities
    {
        public static int GetWeekNumberOfMonth(this DateTime date)
        {
            var cultInfo = System.Globalization.CultureInfo.CreateSpecificCulture("no");
            var cal = cultInfo.Calendar;
            var weekCount = cal.GetWeekOfYear(date, cultInfo.DateTimeFormat.CalendarWeekRule, cultInfo.DateTimeFormat.FirstDayOfWeek);
            return (weekCount - 1) % 4;
            
            // date = date.Date;
            // var firstMonthDay = new DateTime(date.Year, date.Month, 1);
            // var firstMonthMonday = firstMonthDay.AddDays((DayOfWeek.Monday + 7 - firstMonthDay.DayOfWeek) % 7);
            // if (firstMonthMonday > date)
            // {
            //     firstMonthDay = firstMonthDay.AddMonths(-1);
            //     firstMonthMonday = firstMonthDay.AddDays((DayOfWeek.Monday + 7 - firstMonthDay.DayOfWeek) % 7);
            // }
            // return (date - firstMonthMonday).Days / 7 + 1;
        }
    }
}