using System;
using System.IO;
using com.alictus.buildmaster.Editor.Utilities;
using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;

namespace com.alictus.buildmaster.Editor.BuildJobs
{
    public class SetProjectBuildNumber : IPreprocessBuildWithReport
    {
        public int callbackOrder => (int)PreBuildJobType.SetProjectBuildNumber;
        public void OnPreprocessBuild(BuildReport report) => Execute(report.summary.platform);

        private static void Execute(BuildTarget target)
        {
            if (target != BuildTarget.iOS && target != BuildTarget.Android)
                throw new Exception($"Target not supported: {target}");

            BuildLogger.LogInfo("Setting Build number");
            try
            { 
                var projectBuildNumber = int.Parse(ProjectUtilities.GetProjectBuildNumber());
                var localBuildNumber = int.Parse(ProjectUtilities.GetLocalProjectBuildNumber());

                var finalBuildNumber = Math.Max(projectBuildNumber, localBuildNumber);
                ProjectUtilities.SetProjectBuildNumber(finalBuildNumber.ToString());
            }
            catch (Exception e)
            {
                BuildLogger.LogError($"Build number could not be changed.\n{e.Message}");
                throw;
            }
        }
    }
}