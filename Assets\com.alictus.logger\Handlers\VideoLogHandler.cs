using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;
using Cysharp.Threading.Tasks;
using com.alictus.common;
using Debug = UnityEngine.Debug;

namespace com.alictus.logger.runtime
{
    internal class VideoLogHandler
    {
        private readonly CancellationTokenSource _cancellation = new CancellationTokenSource();
        private OfflineLogStore _offlineLogStore;
        private VideoConfig _videoConfig;
        private ISessionProvider _sessionProvider;
        private int _aggregatedUncompressedSize;
        private readonly Stopwatch _lastDispatchTimer = new Stopwatch();
        private int _sentEventCount;

        public static VideoLogHandler Instance { get; private set; }

        public void Initialize(VideoConfig videoConfig, ISessionProvider sessionProvider)
        {
            if (videoConfig == null || !videoConfig.Enabled)
            {
                Debug.Log("[VideoLogHandler] Disabled via config – skipping init.");
                return;
            }

            _videoConfig = videoConfig;
            _sessionProvider = sessionProvider;

            //Persistence for video logs is currently disabled.
            
            /*if (_videoConfig.PersistenceConfig.Enabled)
            {
                _offlineLogStore = new OfflineLogStore();
                _offlineLogStore.Init(_videoConfig.PersistenceConfig);
            }*/

            if (Instance != null)
            {
                Debug.LogWarning("[VideoLogHandler] Another instance already exists – replacing.");
            }
            Instance = this;

            _lastDispatchTimer.Start();
        }

        public void EnqueuePayload(byte[] payload)
        {
            try
            {
                if (payload == null || payload.Length == 0) return;
            
                if (_aggregatedUncompressedSize >= _videoConfig.BatchingConfig.MaxUncompressedSize)
                {
                    return;
                }
            
                _aggregatedUncompressedSize += payload.Length;
                UploadPayload(payload);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[VideoLogHandler] Exception in EnqueuePayload: {ex}");
            }
        }

        public bool CanProcessEvent()
        {
            // Check if we can process an event based on current config limits
            if (_videoConfig == null || !_videoConfig.Enabled) return false;
            if(_videoConfig.MaxCapturesPerSession > 0 && _sentEventCount >= _videoConfig.MaxCapturesPerSession)
            {
                Debug.LogWarning("[VideoLogHandler] Max error captures reached for this session.");
                return false;
            }
            
            bool intervalElapsed = _lastDispatchTimer.ElapsedMilliseconds >= _videoConfig.VideoLogDispatchIntervalSeconds * 1000f;
            return intervalElapsed;
        }
            
        private void UploadPayload(byte[] payload)
        {
            if (payload == null || payload.Length == 0) return;

            // WebRequestManager mirrors LogHandler usage; falls back to offline store on failure.
            try
            {
                var endpoint = _videoConfig.ConnectionConfig.GetEndpoint();
                var req = WebRequestManager.SendRequest(
                    endpoint,
                    _videoConfig.ConnectionConfig.MaxRetries,
                    _videoConfig.ConnectionConfig.Timeout,
                    payload);

                req.OnComplete += HandleRequestComplete;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[VideoLogHandler] Upload exception: {ex}");
                PersistFailedRequest(payload);
            }
        }

        private void HandleRequestComplete(ServerConnection req)
        {
            if (req.CurrentStatus == RequestStatus.Success)
            {
                Debug.Log("[VideoLogHandler] Video payload uploaded successfully.");
                _lastDispatchTimer.Restart();
            }
            else
            {
                Debug.LogError($"[VideoLogHandler] Upload failed with status: {req.CurrentStatus}");
                //PersistFailedRequest(req.GetPayload()); // Disabled for now
            }
        }

        private void PersistFailedRequest(byte[] payload)
        {
            return; // Disabled for now
            if (_videoConfig.PersistenceConfig.Enabled && _offlineLogStore != null)
            {
                _offlineLogStore.SaveFailedRequest(_videoConfig.ConnectionConfig.GetEndpoint(), payload);
            }
        }
    }
}
