using System;
using Cysharp.Threading.Tasks;

namespace com.alictus.common
{
    public interface ISessionProvider
    {
        string PlayerId { get; set; }
        string PlayfabId { get; set; }
        string SessionId { get; set; }
        string DeviceId { get; set; }
        public string BundleId { get; set; }
        string Identifier { get; set; }
        string Platform { get; set; }
        int PlatformId { get; set; }
        bool IsFirstSession { get; set; }
        string DeviceModel { get; set; }
        string EngineVersion { get; set; }
        string GameVersion { get; set; }
        string CountryCode { get; set; }
        int BuildNumber { get; set; }
        string OSVersion { get; set; }
        bool IsInternalUser { get; set; }
        double Timestamp { get; set; }
        public void OnInternalUserDetected(Action<bool> cb);
        public UniTask<string> WaitForDeviceIdCollection();
        public void OnDeviceIdCollected(Action<string> cb);
        public void SetInternalUser(bool isInternalUser);
        public void SetDeviceId(string deviceId);
        public bool TryGetValue(string key, out string value);
    }
}