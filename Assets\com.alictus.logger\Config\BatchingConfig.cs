using System;
using System.Collections.Generic;

namespace com.alictus.logger.runtime
{
    public class BatchingConfig : IBatchingConfig
    {
        public bool Enabled { get; set; } = true;
        public int MaxUncompressedSize { get; set; } = 512 * 1024; // Maximum size of a single batch
        public int MaxEventsPerBatch { get; set; } = 200; // Maximum number of events in a single batch
        public List<int> DispatchIntervalMs { get; set; } = new List<int>(); // Interval between dispatching batches
        private int _dispatchIntervalIndex = 0;
        
        public int GetDispatchInterval()
        {
            if (DispatchIntervalMs == null || DispatchIntervalMs.Count == 0)
            {
                return 5000; // Default value if the list is empty
            }

            var index = Math.Min(_dispatchIntervalIndex, DispatchIntervalMs.Count - 1);
            _dispatchIntervalIndex++; // Increment index for next call, allowing cycling through intervals
            return DispatchIntervalMs[index];
        }
    }
}