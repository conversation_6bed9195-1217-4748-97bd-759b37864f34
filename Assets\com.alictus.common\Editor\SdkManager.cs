using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;
using Debug = UnityEngine.Debug;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Alictus.Common.Editor
{
    public class SdkManager : EditorWindow
    {
        private const string CommonRoot = "Assets/com.alictus.common";
        private const string SdkDataFileName = "sdk_data.json";
        private const string CscRspPath = "Assets/csc.rsp";
        private static string ProjectRootAbsolute => Directory.GetParent(Application.dataPath).FullName;
        private static string ManifestPath => Path.Combine(ProjectRootAbsolute, "Packages", "manifest.json");
        private static string GitIgnorePath => Path.Combine(ProjectRootAbsolute, ".gitignore");

        [MenuItem("SciPlay Turkey/SDK Manager")]        
        public static void ShowWindow()
        {
            GetWindow<SdkManager>(false, "Alictus SDK Manager", true);
        }

        private void OnGUI()
        {
            GUILayout.Space(10);
            if (GUILayout.Button("Enable SDK", GUILayout.Height(40)))
            {
                EnableSdk();
                Close();
            }

            GUILayout.Space(10);
            if (GUILayout.Button("Disable SDK", GUILayout.Height(40)))
            {
                DisableSdk();
                Close();
            }
        }

        public static void EnableSdk()
        {
            var data = LoadSdkData();
            if (data == null) return;

            try
            {
                AddCompilationFlags(data);
                ModifyManifest(data, true);
                AddManifestToGitIgnore();
                AssetDatabase.Refresh();
                HandleGitProjectsEnable(data);
                AssetDatabase.Refresh();
                Debug.Log("[SdkManager] SDK enabled successfully.");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[SdkManager] Enable failed: {ex}");
            }
        }

        private static void DisableSdk()
        {
            var data = LoadSdkData();
            if (data == null) return;
            try
            {
                HandleGitProjectsDisable(data);
                RemoveFolders(data);
                ModifyManifest(data, false);
                AssetDatabase.Refresh();
                Debug.Log("[SdkManager] SDK disabled successfully.");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[SdkManager] Disable failed: {ex}");
            }
        }

        // JSON
        private static SdkData LoadSdkData()
        {
            var path = Path.Combine(CommonRoot, SdkDataFileName);
            if (!File.Exists(path))
            {
                Debug.LogError($"[SdkManager] sdk_data.json not found at {path}");
                return null;
            }
            try
            {
                var json = File.ReadAllText(path);
                return JsonConvert.DeserializeObject<SdkData>(json);
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[SdkManager] Failed to parse sdk_data.json: {ex}");
                return null;
            }
        }
        

        // Compilation Flags
        private static void AddCompilationFlags(SdkData data)
        {
            if (data.GitProjects == null || data.GitProjects.Count == 0) return;

            var flags = new HashSet<string>();
            foreach (var proj in data.GitProjects)
            {
                if (proj.CompilationFlags != null)
                {
                    foreach (var flag in proj.CompilationFlags)
                        flags.Add(flag);
                }
            }
            if (flags.Count == 0) return;

            if (!File.Exists(CscRspPath))
            {
                File.WriteAllText(CscRspPath, "-define:" + string.Join(";", flags));
                Debug.Log("[SdkManager] Created csc.rsp with compilation flags.");
                return;
            }

            var lines = File.ReadAllLines(CscRspPath).ToList();
            var defineIndex = lines.FindIndex(l => l.StartsWith("-define:"));
            if (defineIndex >= 0)
            {
                var existing = lines[defineIndex].Substring("-define:".Length).Split(';');
                var set = new HashSet<string>(existing);
                foreach (var f in flags) set.Add(f);
                lines[defineIndex] = "-define:" + string.Join(";", set);
            }
            else
            {
                lines.Add("-define:" + string.Join(";", flags));
            }
            File.WriteAllLines(CscRspPath, lines);
            Debug.Log("[SdkManager] Updated csc.rsp with compilation flags.");
        }
        

        // Git Projects
        private static void HandleGitProjectsEnable(SdkData data)
        {
            if (data.GitProjects == null) return;
            foreach (var proj in data.GitProjects)
            {
                var absPath = Path.Combine(ProjectRootAbsolute, proj.Path);
                if (Directory.Exists(absPath))
                {
                    Debug.Log($"[SdkManager] Git project already exists at {proj.Path}, skipping clone.");
                    continue;
                }

                if (proj.AsSubmodule)
                {
                    if (!string.IsNullOrEmpty(proj.Branch))
                        RunGit($"submodule add --depth 1 -b {proj.Branch} {proj.URL} {proj.Path}");
                    else
                        RunGit($"submodule add --depth 1 {proj.URL} {proj.Path}");
                }
                else
                {
                    if (!string.IsNullOrEmpty(proj.Branch))
                        RunGit($"clone --depth 1 --branch {proj.Branch} {proj.URL} {proj.Path}");
                    else
                        RunGit($"clone --depth 1 {proj.URL} {proj.Path}");
                }

                if (!string.IsNullOrEmpty(proj.Branch))
                {
                    RunGit($"-C {proj.Path} checkout {proj.Branch}");
                }
                if (!string.IsNullOrEmpty(proj.CommitId))
                {
                    RunGit($"-C {proj.Path} checkout {proj.CommitId}");
                }
            }
        }

        private static void HandleGitProjectsDisable(SdkData data)
        {
            if (data.GitProjects == null) return;
            foreach (var proj in data.GitProjects)
            {
                var absPath = Path.Combine(ProjectRootAbsolute, proj.Path);
                if (Directory.Exists(absPath))
                {
                    try
                    {
                        // Use relative asset path so Unity's AssetDatabase can locate the folder correctly
                        AssetDatabase.DeleteAsset(proj.Path.Replace("\\", "/"));
                        Debug.Log($"[SdkManager] Deleted {proj.Path}");
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[SdkManager] Failed to delete {proj.Path}: {ex.Message}");
                    }
                }
            }
        }
        

        // Manifest
        private static void ModifyManifest(SdkData data, bool add)
        {

            if (!File.Exists(ManifestPath))
            {
                Debug.LogError($"[SdkManager] manifest.json not found at {ManifestPath}");
                return;
            }

            var jManifest = JObject.Parse(File.ReadAllText(ManifestPath));
            var depsObj = jManifest["dependencies"] as JObject ?? new JObject();

            foreach (var kvp in data.Packages)
            {
                if (add)
                    depsObj[kvp.Key] = kvp.Value;
                else
                    depsObj.Remove(kvp.Key);
            }
            jManifest["dependencies"] = depsObj;

            File.WriteAllText(ManifestPath, jManifest.ToString(Formatting.Indented));
            Debug.Log("[SdkManager] manifest.json updated.");

        }

        private static void AddManifestToGitIgnore()
        {
            if (!File.Exists(GitIgnorePath))
            {
                File.WriteAllText(GitIgnorePath, "Packages/manifest.json\n");
                return;
            }
            var lines = File.ReadAllLines(GitIgnorePath).ToList();
            if (!lines.Contains("Packages/manifest.json"))
            {
                lines.Add("Packages/manifest.json");
                File.WriteAllLines(GitIgnorePath, lines);
            }
        }
        

        // Folders
        private static void RemoveFolders(SdkData data)
        {
            if (data.FoldersToRemove == null) return;
            foreach (var folder in data.FoldersToRemove)
            {
                var absPath = Path.Combine(Application.dataPath, folder);
                if (Directory.Exists(absPath))
                {
                    try
                    {
                        // Convert folder name to a relative asset path (Assets/<folder>) for deletion
                        AssetDatabase.DeleteAsset(Path.Combine("Assets", folder).Replace("\\", "/"));
                        Debug.Log($"[SdkManager] Removed folder {folder}");
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[SdkManager] Failed to remove folder {folder}: {ex.Message}");
                    }
                }
            }
        }
        

        // Git Helper
        private static void RunGit(string args)
        {
            var psi = new ProcessStartInfo("git", args)
            {
                WorkingDirectory = ProjectRootAbsolute,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };
            using var proc = Process.Start(psi);
            proc.WaitForExit();
            if (proc.ExitCode != 0)
            {
                Debug.LogError($"[SdkManager] git {args} failed: {proc.StandardError.ReadToEnd()}");
            }
            else
            {
                UnityEngine.Debug.Log($"[SdkManager] git {args} succeeded: {proc.StandardOutput.ReadToEnd()}");
            }
        }
        
    }

    // Data Models
    [Serializable]
    public class GitProject
    {
        public string URL;
        public string Path;
        public string CommitId;
        public string Branch;
        public List<string> CompilationFlags;
        public bool AsSubmodule;
    }

    [Serializable]
    public class SdkData
    {
        public List<GitProject> GitProjects = new List<GitProject>();

        [JsonProperty("Packages")]
        [JsonConverter(typeof(PackagesConverter))]
        public Dictionary<string, string> Packages = new Dictionary<string, string>();

        public List<string> FoldersToRemove = new List<string>();
    }



    public class PackagesConverter : JsonConverter<Dictionary<string, string>>
    {
        public override Dictionary<string, string> ReadJson(JsonReader reader, Type objectType, Dictionary<string, string> existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            var result = new Dictionary<string, string>();
            if (reader.TokenType == JsonToken.StartObject)
            {
                var obj = JObject.Load(reader);
                foreach (var prop in obj.Properties())
                    result[prop.Name] = prop.Value.ToString();
            }
            else if (reader.TokenType == JsonToken.StartArray)
            {
                var array = JArray.Load(reader);
                foreach (var item in array.OfType<JObject>())
                {
                    var prop = item.Properties().FirstOrDefault();
                    if (prop != null)
                        result[prop.Name] = prop.Value.ToString();
                }
            }
            return result;
        }

        public override void WriteJson(JsonWriter writer, Dictionary<string, string> value, JsonSerializer serializer)
        {
            // Serialize as object for simplicity
            serializer.Serialize(writer, value);
        }
    }
    
}
