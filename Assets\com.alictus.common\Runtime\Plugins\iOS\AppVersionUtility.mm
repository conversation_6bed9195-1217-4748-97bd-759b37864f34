extern "C" char* _IOS_BUILD_NUMBER(){
    NSDictionary *info = [[NSBundle mainBundle] infoDictionary];
    NSString *version = [info objectForKey:@"CFBundleVersion"];
        
    return strdup([version UTF8String]);
}
extern "C" char* _IOS_APP_VERSION(){
    NSDictionary *info = [[NSBundle mainBundle] infoDictionary];
    NSString *version = [info objectForKey:@"CFBundleShortVersionString"];
        
    return strdup([version UTF8String]);
}

