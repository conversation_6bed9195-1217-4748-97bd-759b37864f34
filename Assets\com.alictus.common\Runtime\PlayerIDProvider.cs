using System;
using System.Collections.Generic;
using JetBrains.Annotations;
using Newtonsoft.Json;
using UnityEngine;
#if UNITY_IOS && !UNITY_EDITOR
using Newtonsoft.Json.Linq;
#endif

namespace com.alictus.common
{
    public static class PlayerIDProvider
    {
#if UNITY_IOS && !UNITY_EDITOR
        private const string KEYCHAIN_USER_ID = "Match'emAll";
#endif

        private const string ALICTUS_PLAYER_ID_PREF_KEY = "AlictusPlayerID";

        private static string _playerID;
        [CanBeNull] private static string _playerIDWithoutDash;

        public static string GetAlictusPlayerID()
        {
            if (_playerIDWithoutDash is null)
            {
                _playerIDWithoutDash = _playerID.Replace("-", "");
            }

            return _playerIDWithoutDash;
        }

        public static string GetAlictusPlayerIDForLogin()
        {
            return _playerID;
        }

        public static void GeneratePlayerID()
        {
            _playerID = PlayerPrefs.GetString(ALICTUS_PLAYER_ID_PREF_KEY, string.Empty);

            if (!string.IsNullOrEmpty(_playerID))
            {
                return;
            }

            try
            {
                var deviceData = PlayerPrefs.GetString("PlayFab", string.Empty);
                if (deviceData != string.Empty)
                {
                    _playerID = (string)JsonConvert.DeserializeObject<Dictionary<string, object>>(deviceData)["DeviceID"];

                    if (!string.IsNullOrEmpty(_playerID))
                    {
                        Debug.Log("AuthenticationHelper - PlayerID From PlayerPrefs: " + _playerID);
                        UpdateKeyChain(_playerID);
                        return;
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError("Error while getting PlayerID: " + e.Message);
            }


#if UNITY_IOS
#if UNITY_EDITOR
            _playerID = GenerateAndSaveUUIDToKeychain();
#else
            _playerID = LoadFromKeyChain();
            if (string.IsNullOrEmpty(_playerID))
            {
                _playerID = GenerateAndSaveUUIDToKeychain();
                Debug.Log("AuthenticationHelper - PlayerID Not Matched in Keychain " + _playerID);
            }
#endif
#endif

#if UNITY_ANDROID
                Debug.Log("AuthenticationHelper - Keychain is not found!");
                _playerID = GenerateAndSaveUUIDToKeychain();
#endif

            PlayerPrefs.SetString(ALICTUS_PLAYER_ID_PREF_KEY, _playerID);
        }

        private static string GenerateAndSaveUUIDToKeychain()
        {
            Debug.Log("AuthenticationHelper - New ID Generated");
            _playerID = Guid.NewGuid().ToString();
#if UNITY_IOS && !UNITY_EDITOR
            SaveToKeyChain(_playerID);
#endif
            return _playerID;
        }

        public static string LoadFromKeyChain()
        {
#if UNITY_EDITOR
            return GenerateAndSaveUUIDToKeychain();
#elif UNITY_IOS
            var tempUUID = KeyChain.BindGetKeyChainUser();
            var json = JObject.Parse(tempUUID);
            var userId = (string)json["userId"];
            var uuid = (string)json["uuid"];

            string key = "";
            if (userId is KEYCHAIN_USER_ID && !string.IsNullOrEmpty(uuid))
            {
                Debug.Log("AuthenticationHelper - deviceID Found in Keychain " + uuid);
                key = uuid;
            }

            Debug.Log("AuthenticationHelper - LoadKey " + key);
            return key;
#endif
            return string.Empty;
        }
        
        public static void UpdateKeyChain(string playerId)
        {
            _playerID = playerId;
            _playerIDWithoutDash = null;

#if UNITY_IOS && !UNITY_EDITOR
            SaveToKeyChain(_playerID);
#endif
            PlayerPrefs.SetString(ALICTUS_PLAYER_ID_PREF_KEY, _playerID);
        }

#if UNITY_IOS && !UNITY_EDITOR
        public static void SaveToKeyChain(string playerId)
        {
            KeyChain.BindSetKeyChainUser(KEYCHAIN_USER_ID, playerId);
        }

        public static void RemoveFromKeyChain()
        {
            KeyChain.BindSetKeyChainUser(KEYCHAIN_USER_ID, "");
        }
#endif
    }
}
