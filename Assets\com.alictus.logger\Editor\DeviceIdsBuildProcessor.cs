#if UNITY_EDITOR
using System;
using System.IO;
using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEngine;

namespace com.alictus.logger.editor
{
    internal class DeviceIdsBuildProcessor : IPreprocessBuildWithReport
    {
        public int callbackOrder => 0;

        public void OnPreprocessBuild(BuildReport report)
        {
            const string defaultUrl = "https://www.sabrigulerr.com/logger_device_ids.txt";
            string url = defaultUrl;
            try
            {
                using (var client = new System.Net.WebClient())
                {
                    string json = client.DownloadString(url);
                    var ids = JsonConvert.DeserializeObject<List<string>>(json);
                    if (ids == null || ids.Count == 0)
                    {
                        Debug.LogWarning("[Logger] Device IDs list is empty");
                        return;
                    }
                    GenerateSource(ids);
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[Logger] Failed to download device IDs list from {url}. Error: {e}");
            }
        }

        private static void GenerateSource(List<string> ids)
        {
            const string dirPath = "Assets/com.alictus.logger/Runtime";
            const string fileName = "AllowedDeviceIds.generated.cs";
            Directory.CreateDirectory(dirPath);
            string filePath = Path.Combine(dirPath, fileName);

            var sb = new System.Text.StringBuilder();
            sb.AppendLine("#if (UNITY_ANDROID || UNITY_IOS)");
            sb.AppendLine("using System.Collections.Generic;");
            sb.AppendLine("namespace com.alictus.logger.runtime");
            sb.AppendLine("{");
            sb.AppendLine("    internal static partial class AllowedDeviceIds");
            sb.AppendLine("    {");
            sb.AppendLine("        static AllowedDeviceIds()");
            sb.AppendLine("        {");

            string joinedIds = string.Join(",\n                ", ids.ConvertAll(id => $"\"{id}\""));
            sb.AppendLine($"            List.UnionWith(new[] {{\n                {joinedIds}\n            }});");

            sb.AppendLine("        }");
            sb.AppendLine("    }");
            sb.AppendLine("}");
            sb.AppendLine("#endif");

            File.WriteAllText(filePath, sb.ToString());
            AssetDatabase.ImportAsset(filePath);
            Debug.Log("[Logger] Generated embedded AllowedDeviceIds file with " + ids.Count + " entries");
        }
    }
}
#endif 