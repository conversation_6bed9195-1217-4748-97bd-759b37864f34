using System.IO;
using UnityEditor;

namespace com.alictus.buildmaster.Editor.Utilities
{
    public static class CredentialUtilities
    {
        public static string GetCredentials()
        {
            if (string.IsNullOrEmpty(_credentials))
            {
                var credJsonGUIDs = AssetDatabase.FindAssets(BuildConstants.CREDENTIALS_FILENAME,
                    new string[] {
                        "Assets"
                    });
                if (credJsonGUIDs.Length == 0) throw new FileNotFoundException($"{BuildConstants.CREDENTIALS_FILENAME} not found");

                _credentials = File.ReadAllText(AssetDatabase.GUIDToAssetPath(credJsonGUIDs[0]));
            }
            return _credentials;
        }
        private static string _credentials;
    }
}