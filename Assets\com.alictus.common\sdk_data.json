{"GitProjects": [{"URL": "https://bitbucket.org/alictusteam/com.alictus.sdk.git", "Path": "Assets/AlictusSDK", "CommitId": "32af33e823c5fddddd45dd6d71f22b031f313ce9", "Branch": "triple_v2", "CompilationFlags": ["SDK_ENABLED"], "AsSubmodule": false}], "Packages": [{"com.alictus.customtests": "https://bitbucket.org/alictusteam/com.alictus.customtests.git#1.1.0"}, {"com.alictus.googleaccessapi": "https://bitbucket.org/alictusteam/com.alictus.googleaccessapi.git#1.1.1"}, {"com.alictus.ingamepopups": "https://bitbucket.org/alictusteam/com.alictus.ingamepopups.git#1.1.4"}, {"com.alictus.securityutils": "https://bitbucket.org/alictusteam/com.alictus.securityutils.git#1.1.1"}, {"com.gameanalytics.sdk": "https://github.com/GameAnalytics/GA-SDK-UNITY.git#7.10.1"}, {"com.google.android.appbundle": "https://bitbucket.org/alictusteam/com.google.android.appbundle.git#1.9.0"}, {"com.google.external-dependency-manager": "https://bitbucket.org/alictusteam/com.google.external-dependency-manager.git#1.2.182"}, {"com.google.firebase.analytics": "https://bitbucket.org/alictusteam/com.google.firebase.analytics.git#12.1.0"}, {"com.google.firebase.app": "https://bitbucket.org/alictusteam/com.google.firebase.app.git#12.1.0"}, {"com.google.firebase.crashlytics": "https://bitbucket.org/alictusteam/com.google.firebase.crashlytics.git#12.1.0"}, {"com.google.firebase.messaging": "https://bitbucket.org/alictusteam/com.google.firebase.messaging.git#12.1.0"}, {"com.google.play.common": "https://bitbucket.org/alictusteam/com.google.play.common.git#1.9.0"}, {"com.google.play.core": "https://bitbucket.org/alictusteam/com.google.play.core.git#1.8.3"}, {"com.google.play.review": "https://bitbucket.org/alictusteam/com.google.play.review.git#1.8.1"}, {"com.unity.ads.ios-support": "1.2.0"}], "FoldersToRemove": ["FacebookSDK", "AlictusSDKBindings", "AlictusSDKData", "Resources/GameAnalytics"]}