﻿using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO;
using System.Threading;
using com.alictus.common;
using com.alictus.logger.Common;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Debug = UnityEngine.Debug;

namespace com.alictus.logger.runtime
{
    public class LogHandler : IHandler
    {
        private ConcurrentQueue<ILogBase> LogQueue = new ConcurrentQueue<ILogBase>();
        private OfflineLogStore _offlineLogStore;
        private CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
        private LogConfig _logConfig;
        private ISessionProvider _sessionProvider;
        private int _estimatedQueueSize;
        
        public void Initialize(LoggerConfig loggerConfig)
        {
            if(!loggerConfig.LogConfig.Enabled)
            {
                Debug.Log("[Logger] LogHandler is not enabled. Initialization skipped.");
                return;
            }
            
            Debug.Log("[Logger] LogHandler Initialize called");
            _logConfig = loggerConfig.LogConfig;
            _sessionProvider = loggerConfig.sessionProvider;
            Application.logMessageReceivedThreaded += HandleLog;
            if (_logConfig.PersistenceConfig.Enabled)
            {
                _offlineLogStore = new OfflineLogStore();
                _offlineLogStore.Init(_logConfig.PersistenceConfig);
            }
            
            //StartProcessing(_cancellationTokenSource.Token).Forget();
            UniTask.RunOnThreadPool(() => StartProcessing(_cancellationTokenSource.Token));
        }
        
        public void FlushToDisk()
        {
            Debug.Log("FlushToDisk called");
            if (LogQueue.Count == 0)
            {
                Debug.Log("LogQueue is empty, nothing to flush.");
                return;
            }
            
            var serializedData = SerializeQueue(int.MaxValue);
            if (serializedData == null || serializedData.Length == 0)
            {
                Debug.LogWarning("Serialized data is null or empty, nothing to flush.");
                return;
            }
            
            _offlineLogStore.SaveFailedRequest(
                _logConfig.LogConnectionConfig.GetEndpoint(),
                serializedData);
            
            _offlineLogStore.Save();
        }

        public void Deinitialize()
        {
            Debug.Log("DeactivateLogger called");
            Application.logMessageReceivedThreaded -= HandleLog;
            LogQueue.Clear();
        }
        
        private LogData CreateLogData()
        {
            return new LogData()
            {
                Timestamp = Utility.GetUtcTimestamp(),
                EventNumber = EventNumberProvider.GetNextEventNumber(),
            };
        }
        
        private void HandleLog(string logString, string stackTrace, LogType type)
        {
            var logData = CreateLogData();
            logData.LogString = logString;
            logData.StackTrace = stackTrace;
            logData.NativeLogType = Utility.GetLogType(type);
            
            _estimatedQueueSize += logString.Length + stackTrace.Length + 50; // 50 bytes for metadata
            LogQueue.Enqueue(logData);
        }
        
        public void Stop()
        {
            Debug.Log("Stopping LogHandler processing.");
            _cancellationTokenSource.Cancel();
            _cancellationTokenSource.Dispose();
            _cancellationTokenSource = new CancellationTokenSource();
        }
        
        private async UniTaskVoid StartProcessing(CancellationToken token)
        {
            var  lastDispatchTime = new Stopwatch();
            while (!token.IsCancellationRequested)
            {
                try
                {
                    await UniTask.DelayFrame(1, PlayerLoopTiming.Update, token);
                    token.ThrowIfCancellationRequested();
                    if (LogQueue.Count >= _logConfig.BatchingConfig.MaxEventsPerBatch ||
                        _estimatedQueueSize >= _logConfig.BatchingConfig.MaxUncompressedSize ||
                        _logConfig.BatchingConfig.GetDispatchInterval() < lastDispatchTime.ElapsedMilliseconds
                        )
                    {
                        Debug.Log("Processing log events batch.");
                        var serializedData = SerializeBatch();
                        if (serializedData != null && serializedData.Length > 0)
                        {
                            var webReq = WebRequestManager.SendRequest(
                                _logConfig.LogConnectionConfig.Endpoint,
                                _logConfig.LogConnectionConfig.MaxRetries,
                                _logConfig.LogConnectionConfig.Timeout,
                                serializedData);
                            
                            webReq.OnComplete += OnWebRequestComplete;
                            Debug.Log("Serialized data sent successfully.");
                        }
                        else
                        {
                            Debug.LogWarning("Serialized data is null or empty.");
                        }
                        
                        _estimatedQueueSize = 0;
                        lastDispatchTime.Restart();
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception e)
                {
                    Debug.LogError("[Logger] Exception in ProcessEventsLoop: " + e);
                }
            }
        }
        
        private void OnWebRequestComplete(ServerConnection req)
        {
            if (req.CurrentStatus == RequestStatus.Success)
            {
                Debug.Log("Web request completed successfully.");
            }
            else
            {
                if (!_logConfig.PersistenceConfig.Enabled) return;
                Debug.LogError($"Web request failed with status: {req.CurrentStatus}");
                _offlineLogStore.SaveFailedRequest(req.GetEndpoint(), req.GetPayload());
            }
        }

        public byte[] SerializeBatch()
        {
            var serializedQueue = SerializeQueue(_logConfig.BatchingConfig.MaxEventsPerBatch);
            if (serializedQueue == null || serializedQueue.Length == 0)
            {
                Debug.LogWarning("Serialized queue is null or empty.");
                return new byte[] { };
            }
            
            Debug.Log($"Serialized batch size: {serializedQueue.Length} bytes");
            return serializedQueue;
        }

        public byte[] SerializeQueue(int batchSize)
        {
            if (LogQueue.Count == 0) 
            {
                Debug.Log("LogQueue is empty");
                return new byte[] { };
            }

            var messageDataCombined = new BinaryEventArrayData();
            int totalBytes = 0;
            int combinedEventCount = 0;
            
            try
            {
                while (LogQueue.Count > 0 && combinedEventCount < batchSize)
                {
                    using (var ms = new MemoryStream())
                    {
                        using (var logDataBinaryWriter = new BinaryWriter(ms))
                        {
                            LogQueue.TryDequeue(out var logData);
                            if (logData == null) continue;
                            logData.Serialize(logDataBinaryWriter);
                            var r = ms.ToArray();
                            messageDataCombined.Data.Add(r);
                            totalBytes += r.Length;
                            combinedEventCount++;
                        }
                    }
                }
                
                messageDataCombined.ArrayItemCount = combinedEventCount;
                messageDataCombined.IsCompressed = false;
                messageDataCombined.UncompressedSize = totalBytes;
                using (var ms = new MemoryStream())
                {
                    using (var logDataBinaryWriter = new BinaryWriter(ms))
                    {
                        messageDataCombined.Serialize(logDataBinaryWriter);
                        return ms.ToArray();
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError("Exception Occured While Sending the Event: " + e);
            }
            
            return new byte[] { };
        }
    }
}