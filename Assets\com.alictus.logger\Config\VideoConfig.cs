using com.alictus.common;

namespace com.alictus.logger.runtime
{
    public class VideoConfig
    {
        public bool Enabled = true;
        public int BlockSize = 4;
        public int ColorQuantization = 8;
        public int RingBufferFrameCount = 60;
        public int FramesToSkip = 0;
        public int ResolutionDivider = 1; // 1 = original res, 2 = half res
        public int CompressionLevel = 3; // MAX value [22]- Levels 20 and above are called ultra are require more memory and processing power
        public int StreamDurationSeconds = 5;
        public int StreamFrameBatchCount = 12;
        public int ActivationIntervalBetweenRecordingsSeconds = 60;
        public float VideoLogDispatchIntervalSeconds = 15.0f;
        public int MaxCapturesPerSession = 3;
        public BatchingConfig BatchingConfig = new BatchingConfig();
        public ConnectionConfig ConnectionConfig;
        public PersistenceConfig PersistenceConfig = new PersistenceConfig();
        public Filters Filters = new Filters();
        
        public VideoConfig (ISessionProvider sessionProvider)
        {
            ConnectionConfig = new ConnectionConfig(sessionProvider);
        }
    }
}