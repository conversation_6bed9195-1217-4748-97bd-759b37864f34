#if UNITY_IOS

using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEditor.iOS.Xcode;

namespace com.alictus.buildmaster.Editor.BuildJobs
{
    public class iOSSetAppUsesNonExemptEncryption : IPostprocessBuildWithReport
    {
        public int callbackOrder => (int)PostBuildJobType.SetAppUsesNonExemptEncryption;

        public void OnPostprocessBuild(BuildReport report)
        {
            var plistPath = report.summary.outputPath + "/Info.plist";

            var plist = new PlistDocument();
            plist.ReadFromFile(plistPath);

            var rootDict = plist.root;
            rootDict["ITSAppUsesNonExemptEncryption"] = new PlistElementBoolean(false);

            plist.WriteToFile(plistPath);
            BuildLogger.LogInfo("<ITSAppUsesNonExemptEncryption> key set to NO");
        }
    }
}
#endif