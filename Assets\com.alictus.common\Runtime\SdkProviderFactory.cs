﻿/*using com.alictus.common.Editor;
using com.alictus.common.interfaces;

namespace com.alictus.common
{
    public class SdkProviderFactory
    {
        static ISdkProvider _sdkProvider;
        
        public static ISdkProvider GetSdkProvider()
        {
            if (_sdkProvider != null) return _sdkProvider;
            
            #if UNITY_EDITOR
            _sdkProvider =  new DummySdkProvider();
            #endif
            
            if (_sdkProvider == null)
            {
                throw new System.Exception("SdkProvider is not set. Please ensure you have a valid SdkProvider implementation.");
            }
            
            return _sdkProvider;
        }
    }
}*/