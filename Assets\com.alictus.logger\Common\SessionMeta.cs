﻿/*using System.Collections.Generic;
using UnityEngine;

namespace com.alictus.logger.Common
{
    public static class SessionMeta
    {
        private static readonly Dictionary<string, string> Data = new Dictionary<string, string>
        {
            { "SessionId",  ""},
            { "PlayerId",   "" },
            { "PlayfabId",  "" },
            { "DeviceId",   "" },
            { "IsInternalUser", "false"},
            { "Version", Application.version },
            { "Identifier", Application.identifier},
            { "BuildNumber", "1" },
            { "BundleId", "com.ck.tripletiles" },
            { "Platform", Utility.GetPlatformString() },
            { "DeviceModel", "Unknown" },
            { "DeviceName", "Unknown" },
            { "OSVersion", SystemInfo.operatingSystem },
            { "EngineVersion", Application.unityVersion },
            { "Country", "Unknown" },
            { "IsLoggedIn", "false" }
        };
        
        public static string SessionId
        {
            get => Data["SessionId"];
            set => Data["SessionId"] = value;
        }
        
        public static string PlayerId
        {
            get => Data["PlayerId"];
            set => Data["PlayerId"] = value;
        }
        
        public static string PlayfabId
        {
            get => Data["PlayfabId"];
            set => Data["PlayfabId"] = value;
        }

        public static string Version
        {
            get => Data["Version"];
            set => Data["Version"] = value;
        }
        
        public static string BuildNumber
        {
            get => Data["BuildNumber"];
            set => Data["BuildNumber"] = value;
        }
        
        public static string BundleId
        {
            get => Data["BundleId"];
            set => Data["BundleId"] = value;
        }
        
        public static string Platform
        {
            get => Data["Platform"];
            set => Data["Platform"] = value;
        }

        public static string DeviceModel
        {
            get => Data["DeviceModel"];
            set => Data["DeviceModel"] = value;
        }
        
        public static string DeviceName
        {
            get => Data["DeviceName"];
            set => Data["DeviceName"] = value;
        }
        
        public static string OSVersion
        {
            get => Data["OSVersion"];
            set => Data["OSVersion"] = value;
        }
        
        public static string EngineVersion
        {
            get => Data["EngineVersion"];
            set => Data["EngineVersion"] = value;
        }
        
        public static string DeviceId
        {
            get => Data["DeviceId"];
            set => Data["DeviceId"] = value;
        }
        
        public static string Country
        {
            get => Data["Country"];
            set => Data["Country"] = value;
        }
        
        public static bool IsInternalUser
        {
            get => bool.Parse(Data["IsInternalUser"]);
            set => Data["IsInternalUser"] = value.ToString();
        }
        
        public static Dictionary<string, string> Values => Data;
    }
}*/