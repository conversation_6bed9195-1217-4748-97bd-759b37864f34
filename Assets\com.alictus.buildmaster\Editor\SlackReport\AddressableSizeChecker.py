import sys
import requests 
import io 
import os

def send_post_request(url, bearer_token, build_number, message) -> None:
    print(f"Sending \"{message}\" for build \"{build_number}\"")

    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"  # Adjust if necessary
    }
    payload = {
        "build_num": f"{build_number}",
        "message": f"{message}"
    }

    try: 
        requests.post(url, json=payload, headers=headers, timeout=(None, 0.0000000001))
    except requests.exceptions.ReadTimeout:
        pass
 
def get_files_sizes(directory : str) -> list :
    files_with_sizes = []
 
    for f in os.listdir(directory):
        pair = os.path.getsize(os.path.join(directory,f)), f
        files_with_sizes.append(pair)

    return sorted(files_with_sizes, key=lambda x: x[0], reverse=True)

 
def format_message(file_sizes : list, count : int = 10) -> str :
    message = ""
    for pair in file_sizes[:count]:
        if(pair[0] > 1024 * 1024 * 1024):
            size = pair[0]/1024.0/1024.0/1024.0
            message += f"{size:.2f} GB filename: \"{pair[1]}\"\n"

        elif(pair[0] > 1024 * 1024):
            size = pair[0]/1024.0/1024.0
            message += f"{size:.2f} MB filename: \"{pair[1]}\"\n"

        elif(pair[0] > 1024):
            size = pair[0]/1024.0
            message += f"{size:.2f} KB filename: \"{pair[1]}\"\n" 

        else:
            size = pair[0]
            message += f"{size:.2f} B filename: \"{pair[1]}\"\n" 
    return message


def main():
    script_path = sys.argv[0]
    bearer_token = sys.argv[1]
    url = sys.argv[2]
    build_number = sys.argv[3]
    addressable_directory = sys.argv[4]
    file_output = sys.argv[5]
    
    file_sizes = get_files_sizes(addressable_directory)
    slack_message = format_message(file_sizes, 10)
    artifact_text = format_message(file_sizes, len(file_sizes))
  
    send_post_request(url, bearer_token, build_number, slack_message)

    with open(file_output, "w") as f:
        f.write(artifact_text)
    
if __name__ == "__main__":
    main()
