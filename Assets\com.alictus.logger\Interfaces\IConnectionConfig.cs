using System.Collections.Generic;
using com.alictus.common;

namespace com.alictus.logger.runtime
{
    public interface IConnectionConfig
    {
        bool Enabled { get; set; }
        string Endpoint { get; set; }
        int Timeout { get; set; }
        int MaxRetries { get; set; }
        List<int> ExponentialBackoffMs { get; set; }
        Dictionary<string, string> Headers { get; set; }
        public string GetEndpoint();
        public void IncrementExponentialBackoff();
        public int GetExponentialBackoffMs();
    }
}