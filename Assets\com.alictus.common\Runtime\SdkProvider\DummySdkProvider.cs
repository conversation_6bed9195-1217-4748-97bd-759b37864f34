﻿using System;
using System.Collections.Generic;
using com.alictus.common.interfaces;
using UnityEngine.Events;

namespace com.alictus.common.Editor
{
    public class DummySdkProvider : ISdkProvider
    {
        private Dictionary<string, UnityAction<object>> _eventListeners = new Dictionary<string, UnityAction<object>>();
        private Dictionary<string, UnityAction> _eventListenersActionOnly = new Dictionary<string, UnityAction>();
        
        public void RegisterAdIdCallback(Action<string> callback)
        {
            
        }
        
        public void AddListener(string eventName, UnityAction<object> callback)
        {
            _eventListeners.Add(eventName, callback);
        }

        public void AddListener(string eventName, UnityAction callback)
        {
            _eventListenersActionOnly.Add(eventName, callback);
        }

        public void OnEvent(string eventName, UnityAction callback)
        {
            if (_eventListenersActionOnly.TryGetValue(eventName, out UnityAction actionCallback))
            {
                actionCallback.Invoke();
            }
        }

        public void RemoveListener(string eventName, UnityAction callback)
        {
            
        }

        public void RemoveListener(string eventName, UnityAction<object> callback)
        {

        }

        public void OnEvent(string eventName, object payload)
        {
            
        }

        public void RemoveListener(string eventName, Action<object> callback)
        {
            _eventListeners.Remove(eventName);
        }

        public void SendAnalyticEvent(string eventName, Dictionary<string, object> payload)
        {
            OnEvent(eventName, payload);
        }

        public string GetCountry()
        {
            return "";
        }

        public void SetCountry(string country)
        {
            
        }

        public void OnDeviceIdAvailable(Action<string> callback)
        {
            
        }

        public void SendAnalyticEvent<T>(T eventName, Dictionary<string, object> payload)
        {
            
        }

        public void AddGlobalCustomEventFields(Dictionary<string, object> globalCustomEventFields)
        {
            
        }

        public void BindFunctionToEvent(string eventName, Action<object> callback)
        {
            
        }

        public void SendRemoteConfigFetchingCompletedEvent()
        {
            
        }

        public void SendRemoteConfigFetchingStartedEvent()
        {
            
        }

        public bool IsEventEmitted(string eventName)
        {
            return _eventListeners.ContainsKey(eventName);
        }

        public void SendEvent(string eventName, object payload)
        {
            OnEvent(eventName, payload);
        }

        public void SendEvent(string eventName)
        {
            OnEvent(eventName, null);
        }
    }
}