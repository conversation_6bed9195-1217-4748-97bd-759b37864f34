#if UNITY_ANDROID
using System;
using System.IO;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEngine;

namespace com.alictus.logger.editor
{
    public class LoggerPostProcess : IPostprocessBuildWithReport
    {
        public int callbackOrder => Int32.MaxValue;

        public void OnPostprocessBuild(BuildReport report)
        {
            string proguardUserPath = Path.Combine(report.summary.outputPath, "unityLibrary/proguard-user.txt");

            if (!File.Exists(proguardUserPath))
            {
                Debug.LogError("proguardUserPath not found!");
                return;
            }

            var str = File.ReadAllText(proguardUserPath);
            str += "\n-keep class com.alictus.logger.** { *; }";
            str += "\n-keep public class LogServer { *; }";
            str += "\n-keep public class ServerConnection { *; }";
            str += "\n-keep public class EventForwarder { *; }";
            str += "\n-keep public class LogData { *; }";
            str += "\n-keep public class SessionData { *; }";
            str += "\n-keep public class IOSDeviceIdProcessor { *; }";
            str += "\n-keep public class AndroidDeviceIdProcessor { *; }";
            File.WriteAllText(proguardUserPath, str);
            Debug.Log("Added Alictus Logger to proguard!");
        }
    }
}
#endif