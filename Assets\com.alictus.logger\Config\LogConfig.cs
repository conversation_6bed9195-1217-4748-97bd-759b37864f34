using com.alictus.common;

namespace com.alictus.logger.runtime
{
    public class LogConfig
    {
        public bool Enabled = true;
        private ISessionProvider _sessionProvider;
        public BatchingConfig BatchingConfig;
        public ConnectionConfig SessionConnectionConfig;
        public ConnectionConfig LogConnectionConfig;
        public PersistenceConfig PersistenceConfig;
        public Filters Filters = new Filters();
        
        public LogConfig(ISessionProvider sessionProvider)
        {
            _sessionProvider = sessionProvider;
            SessionConnectionConfig  = new ConnectionConfig(_sessionProvider);
            LogConnectionConfig = new ConnectionConfig(_sessionProvider);
            PersistenceConfig = new PersistenceConfig();
            BatchingConfig = new BatchingConfig();
        }
       
    }
}