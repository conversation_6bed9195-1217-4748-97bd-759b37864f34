using System.IO;
using UnityEditor;
using UnityEngine;

namespace com.alictus.buildmaster.Editor.BuildJobs
{
    public static class ChangeAndroidTargetSDK
    {
        const int TargetVersion = 34;
        
        public static void Execute()
        {
            if (!SetTargetSDK())
                ModifyProjectSettings();
        }
        private static bool SetTargetSDK()
        {
            int castedVersion = (int)PlayerSettings.Android.targetSdkVersion;

            if (castedVersion == TargetVersion)
            {
                BuildLogger.LogInfo($"Using Android SDK v{TargetVersion}");
                return true;
            }
            
#if UNITY_2021
            PlayerSettings.Android.targetSdkVersion = (AndroidSdkVersions)TargetVersion; 
            return true;
#endif
            BuildLogger.LogInfo("Could not set target API level in PlayerSettings.Android");
            return false;
        }
        private static void ModifyProjectSettings()
        {
            var projectSettingsPath = Directory.GetParent(Application.dataPath).FullName;
            projectSettingsPath = Path.Combine(projectSettingsPath, "ProjectSettings", "ProjectSettings.asset");
            if (!File.Exists(projectSettingsPath))
            {
                BuildLogger.LogError($"Failed to find ProjectSettings.asset at path: {projectSettingsPath}");
                return;
            }
            var lines = File.ReadAllLines(projectSettingsPath);

            for (var index = 0; index < lines.Length; index++)
            {
                if (lines[index].Contains("AndroidTargetSdkVersion:"))
                {
                    lines[index] = $"  AndroidTargetSdkVersion: {TargetVersion}";
                    break;
                }
            }

            File.WriteAllLines(projectSettingsPath, lines);
            AssetDatabase.ImportAsset(Path.Combine("ProjectSettings", "ProjectSettings.asset"), ImportAssetOptions.ForceUpdate);
            AssetDatabase.Refresh();
            BuildLogger.LogInfo($"Changed AndroidTargetSDK to {TargetVersion} in ProjectSettings.asset");
        }
    }
}