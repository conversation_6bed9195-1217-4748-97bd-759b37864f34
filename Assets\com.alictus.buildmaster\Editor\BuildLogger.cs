using System;
using UnityEngine;

namespace com.alictus.buildmaster
{
    public static class BuildLogger
    {
        public static void LogInfo(string str)
        {
            var formatted = $"[BuildMaster] {str}";
            if (Application.isBatchMode)
                Console.WriteLine(formatted);
            else
                Debug.Log(formatted);
        }
        public static void LogError(string str)
        {
            var formatted = $"[BuildMaster] {str}";

            if (Application.isBatchMode)
                Console.WriteLine(formatted);
            else
                Debug.LogError(formatted);
        }
    }
}