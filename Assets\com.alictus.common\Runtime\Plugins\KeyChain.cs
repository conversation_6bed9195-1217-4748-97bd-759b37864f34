﻿using System;
using UnityEngine;
using System.Runtime.InteropServices;

namespace com.alictus.common
{
    public class KeyChain
    {
#if UNITY_IPHONE || UNITY_STANDALONE_OSX

        [DllImport("__Internal")]
        private static extern string getKeyChainUser();

        public static string BindGetKeyChainUser()
        {
            return getKeyChainUser();
        }

        [DllImport("__Internal")]
        private static extern void setKeyChainUser(string userId, string uuid);

        public static void BindSetKeyChainUser(string userId, string uuid)
        {
            setKeyChainUser(userId, uuid);
        }

        [DllImport("__Internal")]
        private static extern void deleteKeyChainUser();

        public static void BindDeleteKeyChainUser()
        {
            deleteKeyChainUser();
        }

        #region Generic KeyChain Methods

        [DllImport("__Internal")]
        private static extern string getKeyChainValue(string key);
        
        [DllImport("__Internal")]
        private static extern void setKeyChainValue(string key, string value);
        
        [DllImport("__Internal")]
        private static extern void removeKeyChainValue(string key);
        
        [DllImport("__Internal")]
        private static extern void freeKeyChainValue(IntPtr key);
        
        public static string GetKeyChainValue(string key)
        {
            // There is a memory leak here @1k4n
            return getKeyChainValue(key);
        }

        public static void SetKeyChainValue(string key, string value)
        {
            setKeyChainValue(key, value);
        }
        
        public static void RemoveKeyChainValue(string key)
        {
            removeKeyChainValue(key);
        }

        #endregion

#endif
    }
}