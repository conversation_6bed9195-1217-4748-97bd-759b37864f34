﻿using System;
using System.Collections.Generic;
using UnityEngine.Events;

namespace com.alictus.common.interfaces
{
    public interface ISdkProvider
    {
        public void RegisterAdIdCallback(Action<string> callback);
        public void AddListener(string eventName, UnityAction<object> callback);
        public void AddListener(string eventName, UnityAction callback);
        public void OnEvent(string eventName, UnityAction callback);
        public void RemoveListener(string eventName, UnityAction callback);
        public void RemoveListener(string eventName, UnityAction<object> callback);
        public void SendAnalyticEvent(string eventName, Dictionary<string,object> payload);
        public string GetCountry();
        public void SetCountry(string country); 
        public void OnDeviceIdAvailable(Action<string> callback);
        public void SendAnalyticEvent<T>(T eventName, Dictionary<string, object> payload);
        public void AddGlobalCustomEventFields(Dictionary<string, object> globalCustomEventFields);
        public void SendRemoteConfigFetchingCompletedEvent();
        public void SendRemoteConfigFetchingStartedEvent();
        public bool IsEventEmitted(string eventName);
        public void SendEvent(string eventName, object payload);
        public void SendEvent(string eventName);
    }
}