using com.alictus.common;

namespace com.alictus.logger.runtime
{
    public class EventConfig
    {
        public bool Enabled = true;
        public BatchingConfig BatchingConfig;
        public ConnectionConfig ConnectionConfig;
        public PersistenceConfig PersistenceConfig;
        public Filters Filters = new Filters();
        
        public EventConfig(ISessionProvider sessionProvider)
        {
            BatchingConfig = new BatchingConfig();
            ConnectionConfig = new ConnectionConfig(sessionProvider);
            PersistenceConfig = new PersistenceConfig();
        }
    }
}