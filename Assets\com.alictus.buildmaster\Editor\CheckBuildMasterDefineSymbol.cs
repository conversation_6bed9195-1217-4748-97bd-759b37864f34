using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEditor.Build;

namespace com.alictus.sdk.editor
{
    [InitializeOnLoad]
    internal static class CheckBuildMasterDefineSymbol
    {
        private const string Symbol = "ALICTUS_BUILDMASTER_AVAILABLE";

        static CheckBuildMasterDefineSymbol()
        {
            AssemblyReloadEvents.beforeAssemblyReload += CheckSymbolsForSupportedTargets;
        }
        
        private static void CheckSymbolsForSupportedTargets()
        {
            CheckSymbolForBuildTarget(NamedBuildTarget.Android);
            CheckSymbolForBuildTarget(NamedBuildTarget.iOS);
        }

        private static void CheckSymbolForBuildTarget(NamedBuildTarget namedBuildTarget)
        {
            PlayerSettings.GetScriptingDefineSymbols(namedBuildTarget, out var symbols);
            if (symbols.Contains(Symbol)) return;

            var symbolList = new List<string>(symbols);
            symbolList.Add(Symbol);

            PlayerSettings.SetScriptingDefineSymbols(namedBuildTarget, symbolList.ToArray());
        }
    }
}