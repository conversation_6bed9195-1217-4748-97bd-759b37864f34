using System.IO;
using UnityEngine;

namespace com.alictus.buildmaster
{
    public static class BuildConstants
    {
        public static readonly string OUTPUT_DEFAULT_PATH = Path.Combine(Path.GetDirectoryName(Application.dataPath), "Builds");
        public static readonly string DATA_DIRECTORY = Path.Combine(Path.GetDirectoryName(Application.dataPath), "BuildMasterData");

        public const string MENU_ITEM_ROOT = "Alictus/Buildmaster/";

        public const string ICON_FOLDER_NAME = "Icons";
        public const string DEFAULT_ICON_NAME = "icon.png";
        public static readonly string DEFAULT_ICON_FOLDER = Path.Combine("Assets", ICON_FOLDER_NAME);
        public static readonly string DEFAULT_ICON_PATH = Path.Combine(DEFAULT_ICON_FOLDER, DEFAULT_ICON_NAME);

        public const string CREDENTIALS_FILENAME = "com.alictus.buildmaster.credentials";
        public const string DATABASE_SHEET_FILENAME = "com.alictus.buildmaster.sheet_ref";
        public const string JIRA_DOMAIN_FILENAME = "com.alictus.buildmaster.jira_domain";
        public const string JIRA_PROJECT_FILENAME = "com.alictus.buildmaster.jira_project";
        
        public const string DATA_SHEET_ID_ANDROID = "Versions_Android";
        public const string DATA_SHEET_ID_IOS = "Versions_iOS";
  
        public static readonly string PROJECT_BUILD_NUMBER_FILEPATH = Path.Combine(DATA_DIRECTORY, "build_number.txt");
        public static readonly string PROJECT_VERSION_FILEPATH = Path.Combine(DATA_DIRECTORY, "project_version.txt");
        public static readonly string JIRA_CREDENTIALS_FILEPATH = Path.Combine(DATA_DIRECTORY, "create_jira_version");

        public const string GOOGLE_SERVICES_JSON_NAME = "google-services";
    }
}