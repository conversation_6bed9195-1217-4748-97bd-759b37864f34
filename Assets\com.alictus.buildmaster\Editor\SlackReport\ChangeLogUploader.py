import subprocess
import sys
import json
import requests
import re

class JIRAURLFormatter:
     # PP-1234 => <{jira_base_url}/PP-1234|PP-1234> 
  def __init__(self, jira_browse_url: str, limit_value: int): 
    self.limit_value = int(limit_value)
    self.should_format = True
    self.url = str(jira_browse_url)
 
  def Decrement(self): 
    self.limit_value = self.limit_value - 1 
    self.should_format = self.limit_value > 0
 
  def TryFormat(self, text: str) -> str:
    regex = r"\b[A-Z][A-Z0-9_]+-[1-9][0-9]*"

    if self.should_format:
      result = re.sub(regex, lambda m: f"<{self.url}/{m.group(0)}|{m.group(0)}>", text)
      if result != text:
        self.limit_value = self.limit_value - 1 
        self.should_format = self.limit_value > 0
      return result
    else:
      return text
    

def separate_sections_list(list_of_commit_texts: list, search_list: list) -> int:
    size = len(search_list)
    out_list = []
    for i in range(size):
      out_list.append([])
 
    others = []
    for commit_text in list_of_commit_texts: 
      found = False
      for index in range(size):
        if search_list[index] in commit_text: 
          out_list[index].append(commit_text)
          found = True
          break
      if not found:
         others.append(commit_text)
            
    out_list.append(others)
    return out_list
 
def generate_sections_string(header: str, sections_with_commit_texts: dict) -> str :
    out_str = f"{header}\n"
    for section_name, commit_texts in sections_with_commit_texts.items(): 
        out_str += f"\t ● {section_name}\n"
        if len(commit_texts) is 0:
           continue
        
        section_name = commit_texts[0]
        out_str += f"\t\t ┏ {section_name}\n"

        for i in range(1, len(commit_texts) - 1): 
            section_name = commit_texts[i]
            out_str += f"\t\t ┣ {section_name}\n"

        section_name = commit_texts[-1]
        out_str += f"\t\t ┗ {section_name}\n"

    return out_str
 
 
def get_last_commits(commit_hash: str, count: int) -> list:  
    result = subprocess.run(
        ['git', 'rev-list', '--format=%H%x1f%an%x1f%s', '--no-commit-header', f'--max-count={count}', f"{commit_hash}"],
        capture_output=True,
        text=True,
        check=True 
    ) 
    
    commits = []
    for line in result.stdout.strip().split('\n'):
        parts = line.strip().split('\x1f')
        if len(parts) == 3:
            commit_hash, author, subject = parts
            commits.append(subject)
    
    return commits
 
 
def generate_changelog(version: str, list_of_commit_texts: list, url_formatter: JIRAURLFormatter) -> str:
    search_list = ["Feature", "Fix", "Art"]
    list = separate_sections_list(list_of_commit_texts, search_list)
     
     
    sections = {}
    sections["Fix"] = [url_formatter.TryFormat(commit_text) for commit_text in list[1] ]
    sections["Feature"] = [url_formatter.TryFormat(commit_text) for commit_text in list[0] ]
    sections["Art"] = [url_formatter.TryFormat(commit_text) for commit_text in list[2] ]
    sections["Other"] = [url_formatter.TryFormat(commit_text) for commit_text in list[3] ]
    header = f"Version {version} Changelog"
    return generate_sections_string(header, sections)
    
    
def send_post_request(url, bearer_token, build_number, message) -> None:
    print(f"Sending \"{message}\" for build \"{build_number}\"")

    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    payload = {
        "build_num": f"{build_number}",
        "message": message
    } 

    try: 
        requests.post(url, json=payload, headers=headers, timeout=(None, 0.0000000001))
    except requests.exceptions.ReadTimeout:
        pass
 

if __name__ == '__main__': 
  if len(sys.argv) < 8:
    print("Invalid Arguments. Usage: request_url request_bearer_token build_number version commit_hash commit_count jira_url url_format_limit")
    sys.exit(1)
   
  request_url = sys.argv[1]
  request_bearer_token = sys.argv[2]
  build_number = sys.argv[3]  
  version = sys.argv[4]  
  commit_hash = sys.argv[5]
  commit_count = sys.argv[6]
  jira_url = sys.argv[7]
  url_format_limit = sys.argv[8]
    
  formatter = JIRAURLFormatter(jira_url, url_format_limit)
  commit_list = get_last_commits(commit_hash, commit_count)
  message = generate_changelog(version, commit_list,formatter)
  send_post_request(request_url, request_bearer_token, build_number, message)
  sys.exit(0)