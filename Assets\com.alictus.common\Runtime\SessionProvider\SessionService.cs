using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.alictus.common
{
    public class SessionService : ISessionProvider
    {
        private Action<bool> _onInternalUserDetected;
        private Action<string> _onDeviceIdSet;
        private Dictionary<string, string> _propertyDictionary;
        private int? _buildNumberOverride;
        private int _platformIdOverride = -1;
        private static readonly PropertyInfo[] PublicProps =
            typeof(SessionService)
                .GetProperties(BindingFlags.Instance | BindingFlags.Public)
                .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                .ToArray();
        
        public string SessionId { get; set; }
        public string Version { get; set; }
        public string PlayerId { get; set; }
        public string PlayfabId { get; set; }
        public string BundleId { get; set; }
        public string Identifier { get; set; }
        public string Platform { get; set; }
        public int PlatformId
        {
            get =>  _platformIdOverride != -1 ? _platformIdOverride : Utilty.GetPlatformId();
            set => _platformIdOverride = value;
        }

        public string DeviceId { get; set; }
        public bool IsFirstSession { get; set; }
        public string DeviceModel { get; set; }
        public string EngineVersion { get; set; }
        public string GameVersion { get; set; }
        public double UtcTimestamp { get; set; }
        public string CountryCode { get; set; }
        public string OSVersion { get; set; }
        public bool IsInternalUser { get; set; }
        public double Timestamp { get; set; }
        public int BuildNumber {
            get {
                
                
#if UNITY_IOS
            var computed = int.Parse(_IOS_BUILD_NUMBER());
#elif UNITY_ANDROID
                var computed = Utilty.GetBuildBuildNumberAndroid();
#else
        var computed = 1;
#endif
                return _buildNumberOverride ?? computed;
            }
            set {
                _buildNumberOverride = value;
            }
        }

        public void OnInternalUserDetected(Action<bool> cb)
        {
            _onInternalUserDetected += cb;
        }

        public async UniTask<string> WaitForDeviceIdCollection()
        {
            while (string.IsNullOrEmpty(DeviceId))
            {
                await UniTask.Yield();
            }

            return DeviceId;
        }

        public void OnDeviceIdCollected(Action<string> cb)
        {
            _onDeviceIdSet += cb;
        }


        public void SetInternalUser(bool isInternalUser)
        {
            IsInternalUser = isInternalUser;
            _onInternalUserDetected?.Invoke(isInternalUser);
        }

        public void SetDeviceId(string deviceId)
        {
            DeviceId = deviceId;
            _onDeviceIdSet?.Invoke(deviceId);
        }

        private void PopulatePropertyDictionary()
        {
            if(_propertyDictionary == null)
            {
                _propertyDictionary = new Dictionary<string, string>();
                foreach (var p in PublicProps)
                    _propertyDictionary[p.Name] = (string)p.GetValue(this);   // fresh value from THIS instance
            }
        }
        
        public bool TryGetValue(string key, out string inputValue)
        {
            if(_propertyDictionary == null)
                PopulatePropertyDictionary();

            if (!_propertyDictionary.ContainsKey(key))
            {
                inputValue = string.Empty;
                return false;
            }
            
            inputValue = _propertyDictionary[key];
            return true;
        }
        
        #if UNITY_IOS
        [DllImport("__Internal")]
        private static extern string _IOS_BUILD_NUMBER();
    
    
        [DllImport("__Internal")]
        private static extern string _IOS_APP_VERSION();
        #endif

    }
}