using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;

namespace ScreenRecording
{
    /// <summary>
    /// Centralized performance profiling utility for screen recording operations.
    /// Provides consistent timing measurements and logging across all components.
    /// </summary>
    public static class PerformanceProfiler
    {
        private static readonly Dictionary<string, ProfilerSession> _activeSessions = new Dictionary<string, ProfilerSession>();
        private static readonly Dictionary<string, ProfilerStats> _stats = new Dictionary<string, ProfilerStats>();
        
        /// <summary>
        /// Starts a new profiling session for the given operation.
        /// </summary>
        /// <param name="operationName">Name of the operation being profiled</param>
        /// <param name="context">Optional context information</param>
        /// <returns>Session ID for ending the session</returns>
        public static string StartSession(string operationName, string context = null)
        {
            string sessionId = $"{operationName}_{Guid.NewGuid():N}";
            var session = new ProfilerSession
            {
                OperationName = operationName,
                Context = context,
                StartTime = Stopwatch.GetTimestamp(),
                Stopwatch = Stopwatch.StartNew()
            };
            
            _activeSessions[sessionId] = session;
            
            if (!string.IsNullOrEmpty(context))
            {
                UnityEngine.Debug.Log($"[PerformanceProfiler] Started: {operationName} ({context})");
            }
            else
            {
                UnityEngine.Debug.Log($"[PerformanceProfiler] Started: {operationName}");
            }
            
            return sessionId;
        }
        
        /// <summary>
        /// Ends a profiling session and logs the results.
        /// </summary>
        /// <param name="sessionId">Session ID returned from StartSession</param>
        /// <param name="additionalInfo">Optional additional information to log</param>
        public static void EndSession(string sessionId, string additionalInfo = null)
        {
            if (!_activeSessions.TryGetValue(sessionId, out var session))
            {
                UnityEngine.Debug.LogWarning($"[PerformanceProfiler] Session not found: {sessionId}");
                return;
            }
            
            session.Stopwatch.Stop();
            double elapsedMs = session.Stopwatch.Elapsed.TotalMilliseconds;
            
            // Update statistics
            UpdateStats(session.OperationName, elapsedMs);
            
            // Log the result
            string logMessage = $"[PerformanceProfiler] Completed: {session.OperationName} - {elapsedMs:F2}ms";
            if (!string.IsNullOrEmpty(session.Context))
            {
                logMessage += $" ({session.Context})";
            }
            if (!string.IsNullOrEmpty(additionalInfo))
            {
                logMessage += $" - {additionalInfo}";
            }
            
            UnityEngine.Debug.Log(logMessage);
            
            _activeSessions.Remove(sessionId);
        }
        
        /// <summary>
        /// Times a single operation and logs the result.
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="operation">The operation to time</param>
        /// <param name="context">Optional context information</param>
        /// <returns>The elapsed time in milliseconds</returns>
        public static double TimeOperation(string operationName, Action operation, string context = null)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                operation();
            }
            finally
            {
                stopwatch.Stop();
                double elapsedMs = stopwatch.Elapsed.TotalMilliseconds;
                
                UpdateStats(operationName, elapsedMs);
                
                string logMessage = $"[PerformanceProfiler] {operationName}: {elapsedMs:F2}ms";
                if (!string.IsNullOrEmpty(context))
                {
                    logMessage += $" ({context})";
                }
                
                UnityEngine.Debug.Log(logMessage);
            }
            
            return stopwatch.Elapsed.TotalMilliseconds;
        }
        
        /// <summary>
        /// Times a single operation and returns both the result and elapsed time.
        /// </summary>
        /// <typeparam name="T">Return type of the operation</typeparam>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="operation">The operation to time</param>
        /// <param name="context">Optional context information</param>
        /// <returns>Tuple containing the operation result and elapsed time in milliseconds</returns>
        public static (T result, double elapsedMs) TimeOperation<T>(string operationName, Func<T> operation, string context = null)
        {
            var stopwatch = Stopwatch.StartNew();
            T result;
            
            try
            {
                result = operation();
            }
            finally
            {
                stopwatch.Stop();
                double elapsedMs = stopwatch.Elapsed.TotalMilliseconds;
                
                UpdateStats(operationName, elapsedMs);
                
                string logMessage = $"[PerformanceProfiler] {operationName}: {elapsedMs:F2}ms";
                if (!string.IsNullOrEmpty(context))
                {
                    logMessage += $" ({context})";
                }
                
                UnityEngine.Debug.Log(logMessage);
            }
            
            return (result, stopwatch.Elapsed.TotalMilliseconds);
        }
        
        /// <summary>
        /// Logs performance statistics for all tracked operations.
        /// </summary>
        public static void LogStatistics()
        {
            if (_stats.Count == 0)
            {
                UnityEngine.Debug.Log("[PerformanceProfiler] No statistics available.");
                return;
            }
            
            UnityEngine.Debug.Log("[PerformanceProfiler] === Performance Statistics ===");
            
            foreach (var kvp in _stats)
            {
                var stats = kvp.Value;
                UnityEngine.Debug.Log($"[PerformanceProfiler] {kvp.Key}:");
                UnityEngine.Debug.Log($"  Count: {stats.Count}, Avg: {stats.Average:F2}ms, Min: {stats.Min:F2}ms, Max: {stats.Max:F2}ms, Total: {stats.Total:F2}ms");
            }
        }
        
        /// <summary>
        /// Clears all performance statistics.
        /// </summary>
        public static void ClearStatistics()
        {
            _stats.Clear();
            UnityEngine.Debug.Log("[PerformanceProfiler] Statistics cleared.");
        }
        
        private static void UpdateStats(string operationName, double elapsedMs)
        {
            if (!_stats.TryGetValue(operationName, out var stats))
            {
                stats = new ProfilerStats();
                _stats[operationName] = stats;
            }
            
            stats.Count++;
            stats.Total += elapsedMs;
            stats.Average = stats.Total / stats.Count;
            stats.Min = stats.Count == 1 ? elapsedMs : Math.Min(stats.Min, elapsedMs);
            stats.Max = stats.Count == 1 ? elapsedMs : Math.Max(stats.Max, elapsedMs);
        }
        
        private class ProfilerSession
        {
            public string OperationName { get; set; }
            public string Context { get; set; }
            public long StartTime { get; set; }
            public Stopwatch Stopwatch { get; set; }
        }
        
        private class ProfilerStats
        {
            public int Count { get; set; }
            public double Total { get; set; }
            public double Average { get; set; }
            public double Min { get; set; }
            public double Max { get; set; }
        }
    }
}
