using System;
using System.IO;
using UnityEditor;

namespace com.alictus.buildmaster.Editor.Utilities
{
    public static class ProjectUtilities
    {
        public static void SetProjectVersion(string version)
        {
            var oldVersion = PlayerSettings.bundleVersion;
            PlayerSettings.bundleVersion = version;
            BuildLogger.LogInfo($"Project version set: {oldVersion} -> {version}");
        }
        public static string GetProjectVersion()
        {
            return PlayerSettings.bundleVersion;
        }

        public static void SetProjectBuildNumber(string buildNumber)
        {
            var oldBuildNumber = GetProjectBuildNumber();
#if UNITY_IOS
            PlayerSettings.iOS.buildNumber = buildNumber;
#elif UNITY_ANDROID
            PlayerSettings.Android.bundleVersionCode = int.Parse(buildNumber);
#endif
            BuildLogger.LogInfo($"Project Build Number set: {oldBuildNumber} -> {buildNumber}");
        }
        public static string GetProjectBuildNumber()
        {
#if UNITY_IOS
            return PlayerSettings.iOS.buildNumber;
#elif UNITY_ANDROID
            return PlayerSettings.Android.bundleVersionCode.ToString();
#endif
            return "0"; // Default value if not set
        }

        public static void SaveToLocal()
        {
            if (!Directory.Exists(BuildConstants.DATA_DIRECTORY))
                Directory.CreateDirectory(BuildConstants.DATA_DIRECTORY);

            File.WriteAllText(BuildConstants.PROJECT_VERSION_FILEPATH, GetProjectVersion());
            File.WriteAllText(BuildConstants.PROJECT_BUILD_NUMBER_FILEPATH, GetProjectBuildNumber());
            BuildLogger.LogInfo($"Project settings saved");
        }
        public static string GetLocalProjectVersion()
        {
            if (!File.Exists(BuildConstants.PROJECT_VERSION_FILEPATH))
                throw new Exception($"Failed to read version from: {BuildConstants.PROJECT_VERSION_FILEPATH}");

            return File.ReadAllText(BuildConstants.PROJECT_VERSION_FILEPATH).Trim();
        }
        public static string GetLocalProjectBuildNumber()
        {
            if (!File.Exists(BuildConstants.PROJECT_BUILD_NUMBER_FILEPATH))
                throw new Exception($"Failed to read build number from: {BuildConstants.PROJECT_BUILD_NUMBER_FILEPATH}");
            
            return File.ReadAllText(BuildConstants.PROJECT_BUILD_NUMBER_FILEPATH).Trim();
        }
    }
}