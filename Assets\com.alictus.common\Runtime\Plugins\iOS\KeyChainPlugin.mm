#import "KeyChainPlugin.h"
#import "UICKeyChainStore.h"

NSString *_keyForID = @"UserID";
NSString *_keyForUUID = @"UserUUID";

@implementation KeyChainPlugin

extern "C" {
    char* getKeyChainUser();
    void setKeyChainUser(const char* userId, const char* uuid);
    void deleteKeyChainUser();
    char* getKeyChainValue(const char *key);
    void setKeyChainValue(const char *key, const char *value);
    void removeKeyChainValue(const char *key);
    void freeKeyChainValue(const char *value);
}

char* getKeyChainUser()
{
    NSString *userId = [UICKeyChainStore stringForKey:_keyForID];
    NSString *userUUID = [UICKeyChainStore stringForKey:_keyForUUID];

    if (userId == nil || [userId isEqualToString:@""]) {
        NSLog(@"No user information");
        userId = @"";
        userUUID = @"";
    }

    NSString* json = [NSString stringWithFormat:@"{\"userId\":\"%@\",\"uuid\":\"%@\"}",userId,userUUID];

    return makeStringCopy([json UTF8String]);
}

void setKeyChainUser(const char* userId, const char* uuid)
{
    NSString *nsUseId = [NSString stringWithCString: userId encoding:NSUTF8StringEncoding];
    NSString *nsUUID = [NSString stringWithCString: uuid encoding:NSUTF8StringEncoding];

    

    [UICKeyChainStore setString:nsUseId forKey:_keyForID];
    [UICKeyChainStore setString:nsUUID forKey:_keyForUUID];
}

void deleteKeyChainUser()
{
    [UICKeyChainStore removeItemForKey:_keyForID];
    [UICKeyChainStore removeItemForKey:_keyForUUID];
}

char* getKeyChainValue(const char *key)
{
    if (key == NULL)
    {
        NSLog(@"KeyChain Error: Cannot get keychain value, key is null");
        return NULL;
    }

    NSString *nsKey = [NSString stringWithCString:key encoding:NSUTF8StringEncoding];
    NSString *nsValue = [UICKeyChainStore stringForKey:nsKey];
    
    return makeStringCopy([nsValue UTF8String]);
}

void setKeyChainValue(const char *key, const char *value)
{
    if (key == NULL || value == NULL)
    {
        NSLog(@"KeyChain Error: Cannot set keychain value, key or value is null");
        return;
    }

    NSString *nsKey = [NSString stringWithCString:key encoding:NSUTF8StringEncoding];
    NSString *nsValue = [NSString stringWithCString:value encoding:NSUTF8StringEncoding];

    if (checkDataSize(nsKey) || checkDataSize(nsValue))
    {
        NSLog(@"KeyChain Error: Key or value exceeds 4KB limit");
        return;
    }
    
    [UICKeyChainStore setString:nsValue forKey:nsKey];
}

void removeKeyChainValue(const char *key)
{
    if (key == NULL)
    {
        NSLog(@"KeyChain Error: Cannot remove keychain value, key is null");
        return;
    }

    NSString *nsKey = [NSString stringWithCString:key encoding:NSUTF8StringEncoding];
    
    [UICKeyChainStore removeItemForKey:nsKey];
}

char* makeStringCopy(const char *str)
{
    if (str == NULL) {
        return NULL;
    }

    char* res = (char*)malloc(strlen(str) + 1);
    strcpy(res, str);
    return res;
}

void freeKeyChainValue(char *ptr)
{
    if (ptr != NULL)
    {
        free(ptr);
    }
}

BOOL checkDataSize(NSString *data)
{
    NSData *utf8Data = [data dataUsingEncoding:NSUTF8StringEncoding];
    return ([utf8Data length] > 4096);
}

@end
