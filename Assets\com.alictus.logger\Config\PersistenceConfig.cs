namespace com.alictus.logger.runtime
{
    public class PersistenceConfig : IPersistenceConfig
    {
        public bool Enabled { get; set; } = true;
        public string StoreName { get; set; } = "LoggerData";
        public string KeyPrefix { get; set; } = "Log_"; 
        public int FlushSizeBytes { get; set; } = 128 * 1024; // Flush to disk after this many bytes
        public int FlushIntervalMs { get; set; } = 60; // Interval to flush logs to disk
    }
}