using System;
using System.IO;
using UnityEditor;

namespace com.alictus.buildmaster.Editor.Utilities
{
    public static class JiraUtilities
    {
        public static string GetDomain()
        {
            if (string.IsNullOrEmpty(_domain))
            {
                var credJsonGUIDs = AssetDatabase.FindAssets(BuildConstants.JIRA_DOMAIN_FILENAME,
                    new string[] {
                        "Assets"
                    });
                if (credJsonGUIDs.Length == 0) throw new FileNotFoundException($"{BuildConstants.JIRA_PROJECT_FILENAME} not found");

                _domain = File.ReadAllText(AssetDatabase.GUIDToAssetPath(credJsonGUIDs[0])).Trim();
                BuildLogger.LogInfo($"Using \"{_domain}\" as jira domain");
            }
            return _domain;
        }
        public static string GetProject()
        {
            if (string.IsNullOrEmpty(_domain))
            {
                var credJsonGUIDs = AssetDatabase.FindAssets(BuildConstants.JIRA_PROJECT_FILENAME,
                    new string[] {
                        "Assets"
                    });
                if (credJsonGUIDs.Length == 0) throw new FileNotFoundException($"{BuildConstants.JIRA_PROJECT_FILENAME} not found");

                _project = File.ReadAllText(AssetDatabase.GUIDToAssetPath(credJsonGUIDs[0])).Trim();
                BuildLogger.LogInfo($"Using \"{_project}\" as jira project");
            }
            return _project;
        }
        public static string GetCredentials()
        {
            if (string.IsNullOrEmpty(_credentials))
            {
                var credStr = File.ReadAllText(BuildConstants.JIRA_CREDENTIALS_FILEPATH).Trim();
                _credentials =  Convert.ToBase64String(System.Text.Encoding.GetEncoding("ISO-8859-1").GetBytes(credStr));
            }
            return _credentials;
        }
        public static bool ShouldSendNewRelease()
        {
            return File.Exists(BuildConstants.JIRA_CREDENTIALS_FILEPATH);
        }

        private static string _domain;
        private static string _project;
        private static string _credentials;
    }
}