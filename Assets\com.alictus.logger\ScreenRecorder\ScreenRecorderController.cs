using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Rendering; // For AsyncGPUReadback
using UnityEngine.SceneManagement; // To detect scene changes
using Unity.Collections;
using Debug = UnityEngine.Debug;
using com.alictus.logger.runtime;

namespace ScreenRecording
{
    public class ScreenRecorderController : MonoBehaviour
    {
        [Header("Capture Settings")]
        private readonly RenderTextureFormat captureFormat = RenderTextureFormat.ARGB32;
        private readonly TextureFormat readbackFormat = TextureFormat.ARGB32; // e.g., TextureFormat.RGBA32 for Color32 or ARGB32 for bytes

        [SerializeField] private int framesToSkip = 0;
        private int currentFrameCounter = 0;
        private bool forceCaptureNextFrame = false;
        [SerializeField] private int ringBufferFrameCount = 60;
        [SerializeField] private int blockSize = 4;
        [SerializeField] private int colorQuantization = 2;
        [SerializeField] private int compressionLevel = 3;
        [SerializeField] private float streamDurationSeconds = 5f;
        [SerializeField] private int streamFrameBatchCount = 12;
        [SerializeField] private int activationIntervalBetweenRecordingsSeconds = 60;
        [SerializeField] private float videoLogDispatchIntervalSeconds = 15f;

        // Runtime helpers
        private float recordingSegmentStartTime;
        private int framesCapturedInSegment;
        private bool isStreaming = false;
        private float streamEndTime = 0f;
        private int errorCapturesDone = 0;

        private RenderTexture rt1, rt2;
        private RenderTexture activeWriteRT;
        private RenderTexture pendingReadbackRT;

        private FrameRingBuffer frameRingBuffer;

        private bool isRecording = false;
        private Coroutine captureCoroutine;
        private CommandBuffer captureCommandBuffer;
        private Camera targetCamera; // Cache the camera that has our command buffer
 
        private int actualCaptureWidth;
        private int actualCaptureHeight;
        private VideoConfig _videoConfig;
        private int targetWidth;
        private int targetHeight;

        public static ScreenRecorderController Initialize(VideoConfig videoConfig)
        {
            Debug.Log("ScreenRecorderController initialized with VideoConfig.");
            var gameObject = new GameObject("ScreenRecorderController");
            DontDestroyOnLoad(gameObject); // Ensure it persists across scenes
            var controller = gameObject.AddComponent<ScreenRecorderController>();
            controller._videoConfig = videoConfig;
            controller.targetWidth = Screen.width / videoConfig.ResolutionDivider;
            controller.targetHeight = Screen.height / videoConfig.ResolutionDivider;
            controller.framesToSkip = videoConfig.FramesToSkip;
            controller.blockSize = videoConfig.BlockSize;
            controller.colorQuantization = videoConfig.ColorQuantization;
            controller.compressionLevel = videoConfig.CompressionLevel;
            controller.streamDurationSeconds = videoConfig.StreamDurationSeconds;
            controller.streamFrameBatchCount = videoConfig.StreamFrameBatchCount;
            controller.activationIntervalBetweenRecordingsSeconds = videoConfig.ActivationIntervalBetweenRecordingsSeconds;
            controller.videoLogDispatchIntervalSeconds = videoConfig.VideoLogDispatchIntervalSeconds;
            controller.ringBufferFrameCount = videoConfig.RingBufferFrameCount;
            controller.frameRingBuffer = new FrameRingBuffer(
                videoConfig.RingBufferFrameCount,
                Screen.width / videoConfig.ResolutionDivider,
                Screen.height / videoConfig.ResolutionDivider);
            controller.enabled = true;
            controller.InitializeTextures();
            
            return controller;
        }

        void Awake()
        {
            // Subscribe to scene change events so we can re-attach our CommandBuffer when a new scene loads
            SceneManager.activeSceneChanged += OnActiveSceneChanged;
        }

        void InitializeTextures()
        {
            if (targetWidth <= 0)
            {
                Debug.LogError("Target Width must be positive. Disabling recorder.");
                enabled = false;
                return;
            }

            actualCaptureWidth = targetWidth;
            if (targetHeight <= 0)
            {
                actualCaptureHeight = Mathf.RoundToInt(targetWidth / ((float)Screen.width / Screen.height));
            }
            else
            {
                actualCaptureHeight = targetHeight;
            }
            
            Debug.Log($"Screen Recorder Initializing. Capture Resolution: {actualCaptureWidth}x{actualCaptureHeight}");

            try
            {
                rt1 = new RenderTexture(actualCaptureWidth, actualCaptureHeight, 0, captureFormat);
                rt2 = new RenderTexture(actualCaptureWidth, actualCaptureHeight, 0, captureFormat);
                rt1.Create();
                rt2.Create();

                activeWriteRT = rt1;
                pendingReadbackRT = rt2;

                frameRingBuffer = new FrameRingBuffer(ringBufferFrameCount, actualCaptureWidth, actualCaptureHeight);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error initializing ScreenRecorderController: {ex.Message}");
                enabled = false;
            }
        }

        public void StartRecording()
        {
            if (isRecording)
            {
                Debug.LogWarning("Recording is already in progress.");
                return;
            }
            
            if (!enabled || rt1 == null || rt2 == null || frameRingBuffer == null)
            {
                if (!enabled)
                {
                    Debug.LogError("ScreenRecorderController is not enabled. Cannot start recording.");
                    return;
                }
                
                if (rt1 == null || rt2 == null)
                {
                    Debug.LogError("RenderTextures are not initialized. Cannot start recording.");
                    return;
                }
                
                if (frameRingBuffer == null)
                {
                    Debug.LogError("FrameRingBuffer is not initialized. Cannot start recording.");
                    return;
                }
                
                Debug.LogError("ScreenRecorder is not properly initialized. Cannot start recording.");
                return;
            }

            // Create and add CommandBuffer
            if (captureCommandBuffer == null)
            {
                captureCommandBuffer = new CommandBuffer { name = "ScreenRecordBlit" };
            }
            else
            {
                captureCommandBuffer.Clear(); // Clear any previous commands if reusing
            }

            captureCommandBuffer.Blit(BuiltinRenderTextureType.CurrentActive, activeWriteRT); // Blit camera's current active RT to our active RT
            
            targetCamera = Camera.main;
            if (targetCamera == null)
            {
                Debug.LogError("No Main Camera found for ScreenRecorder. Recording aborted.");
                return;
            }
            targetCamera.AddCommandBuffer(CameraEvent.AfterSkybox, captureCommandBuffer);
 
            isRecording = true;
            if (isStreaming)
            {
                recordingSegmentStartTime = Time.time;
                streamEndTime = Time.time + streamDurationSeconds;
                framesCapturedInSegment = 0;
            }
            currentFrameCounter = 0;
            forceCaptureNextFrame = false; // Reset any pending forced capture
            Debug.Log("Screen Recording Started.");
            if (captureCoroutine != null) StopCoroutine(captureCoroutine);
            captureCoroutine = StartCoroutine(CaptureLoop());
        }

        public void StopRecording()
        {
            if (!isRecording)
            {
                return;
            }

            isRecording = false;
            if (captureCoroutine != null)
            {
                StopCoroutine(captureCoroutine);
                captureCoroutine = null;
            }

            // Remove and release CommandBuffer
            if (targetCamera != null && captureCommandBuffer != null)
            {
                targetCamera.RemoveCommandBuffer(CameraEvent.AfterSkybox, captureCommandBuffer);
                targetCamera = null;
            }

            Debug.Log("Screen Recording Stopped.");
            // Reset segment counters so next recording starts fresh
            framesCapturedInSegment = 0;

            // Encode and compress the captured frames using the specified compression level.
            string finalEncodeSessionId = PerformanceProfiler.StartSession("Final_Encode_On_Stop", $"Frames: {frameRingBuffer.Count}");

            try
            {
                var (encoder, encoderCreateTime) = PerformanceProfiler.TimeOperation("Create_Final_FrameSequenceEncoder", () => {
                    return new FrameSequenceEncoder(blockSize, colorQuantization, compressionLevel);
                }, $"BlockSize: {blockSize}, Quantization: {colorQuantization}, Compression: {compressionLevel}");

                var (payload, encodeTime) = PerformanceProfiler.TimeOperation("Final_Encode_Sequence", () => {
                    return encoder.Encode(frameRingBuffer);
                }, $"Frames: {frameRingBuffer.Count}");

                if (payload != null)
                {
                    Debug.Log($"[ScreenRecorderController] Encoded & compressed clip size: {payload.Length} bytes using compression level {compressionLevel}.");

                    PerformanceProfiler.TimeOperation("Final_Enqueue_Payload", () => {
                        VideoLogHandler.Instance?.EnqueuePayload(payload);
                    }, $"Size: {payload.Length} bytes");
                }

                PerformanceProfiler.EndSession(finalEncodeSessionId, $"Success - {payload?.Length ?? 0} bytes");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ScreenRecorderController] Failed to encode/compress frames: {ex.Message}");
                PerformanceProfiler.EndSession(finalEncodeSessionId, $"Exception: {ex.Message}");
            }
        }
 
        private IEnumerator CaptureLoop()
        {
            while (isRecording)
            {
                string frameSessionId = PerformanceProfiler.StartSession("Frame_Capture_Cycle", $"Frame {currentFrameCounter}");

                yield return new WaitForEndOfFrame(); // Wait for all rendering to complete

                bool shouldCaptureThisFrame = forceCaptureNextFrame || (currentFrameCounter % (framesToSkip + 1) == 0);
                forceCaptureNextFrame = false; // Reset flag

                if (shouldCaptureThisFrame)
                {
                    PerformanceProfiler.TimeOperation("RT_Swap_And_Blit", () => {
                        RenderTexture temp = activeWriteRT;
                        activeWriteRT = pendingReadbackRT;
                        pendingReadbackRT = temp; // pendingReadbackRT now holds the frame captured by the CommandBuffer.

                        if (captureCommandBuffer != null) // targetCamera null check is done in StartRecording
                        {
                            captureCommandBuffer.Clear(); // Clear previous commands
                            captureCommandBuffer.Blit(BuiltinRenderTextureType.CurrentActive, activeWriteRT);
                        }
                    });

                    PerformanceProfiler.TimeOperation("GPU_Readback_Request", () => {
                        AsyncGPUReadback.Request(pendingReadbackRT, 0, readbackFormat, OnReadbackComplete);
                    });

                    PerformanceProfiler.EndSession(frameSessionId, "Captured");
                }
                else
                {
                    PerformanceProfiler.EndSession(frameSessionId, "Skipped");
                }

                currentFrameCounter++;
                if (isStreaming) framesCapturedInSegment++;

                if (isStreaming)
                {
                    bool batchReady = streamFrameBatchCount > 0 && framesCapturedInSegment >= streamFrameBatchCount;
                    if (batchReady)
                    {
                        PerformanceProfiler.TimeOperation("Encode_Ring_Buffer_Batch", () => {
                            EncodeCurrentRingBuffer();
                        }, $"Batch size: {framesCapturedInSegment}");
                        framesCapturedInSegment = 0;
                    }
                    if (Time.time >= streamEndTime)
                    {
                        isStreaming = false;
                        framesCapturedInSegment = 0;
                    }
                }
            }
        }

        void OnReadbackComplete(AsyncGPUReadbackRequest request)
        {
            string sessionId = PerformanceProfiler.StartSession("GPU_Readback_Complete", $"Frame {Time.frameCount}");

            try
            {
                if (!isRecording && !forceCaptureNextFrame) // Check if still relevant
                {
                    // If recording stopped while readback was in flight, or if it was a forced capture that's no longer needed.
                    PerformanceProfiler.EndSession(sessionId, "Skipped - recording stopped");
                    return;
                }

                if (request.hasError)
                {
                    Debug.LogError("GPU readback error!");
                    PerformanceProfiler.EndSession(sessionId, "Error - GPU readback failed");
                    return;
                }
                if (request.done) // Ensure it's truly done
                {
                    string bufferSessionId = PerformanceProfiler.StartSession("Buffer_Write_Operation");

                    RawFrameData targetSlot = frameRingBuffer.GetSlotForWrite();
                    if (targetSlot != null)
                    {
                        NativeArray<byte> data = request.GetData<byte>();

                        if (data.Length == targetSlot.PixelData.Length)
                        {
                            PerformanceProfiler.TimeOperation("NativeArray_Copy", () => {
                                NativeArray<byte>.Copy(data, targetSlot.PixelData, data.Length);
                            }, $"Size: {data.Length} bytes");

                            frameRingBuffer.CommitWrite(Time.frameCount); // Using Time.frameCount as an approximation of game frame number
                            PerformanceProfiler.EndSession(bufferSessionId, $"Success - {data.Length} bytes");
                        }
                        else
                        {
                            Debug.LogError($"Readback data length ({data.Length}) does not match target slot length ({targetSlot.PixelData.Length}).");
                            PerformanceProfiler.EndSession(bufferSessionId, "Error - size mismatch");
                        }
                    }
                    else
                    {
                        PerformanceProfiler.EndSession(bufferSessionId, "Error - no target slot");
                    }
                }

                PerformanceProfiler.EndSession(sessionId, "Completed successfully");
            }
            catch (Exception ex)
            {
                PerformanceProfiler.EndSession(sessionId, $"Exception: {ex.Message}");
                throw;
            }
        }

        private void OnActiveSceneChanged(Scene oldScene, Scene newScene)
        {
            // If we are recording, we need to re-attach our CommandBuffer to the new scene's camera
            if (!isRecording) return;
            ReattachCommandBuffer();
        }

        private void ReattachCommandBuffer()
        {
            if (captureCommandBuffer == null)
            {
                return;
            }

            // Remove from old camera if it exists
            if (targetCamera != null)
            {
                targetCamera.RemoveCommandBuffer(CameraEvent.AfterSkybox, captureCommandBuffer);
            }

            // Grab the new scene's main camera
            targetCamera = Camera.main;
            if (targetCamera == null)
            {
                Debug.LogWarning("No Main Camera found after scene change – screen recording will pause until a camera is available.");
                return;
            }

            // Re-configure and add the CommandBuffer
            captureCommandBuffer.Clear();
            captureCommandBuffer.Blit(BuiltinRenderTextureType.CurrentActive, activeWriteRT);
            targetCamera.AddCommandBuffer(CameraEvent.AfterSkybox, captureCommandBuffer);
        }

        public void HandleErrorTrigger()
        {
            int maxCaptures = _videoConfig?.MaxCapturesPerSession ?? 3;
            if (errorCapturesDone >= maxCaptures)
            {
                Debug.Log("[ScreenRecorderController] Max error captures reached – ignoring trigger.");
                return;
            }
            errorCapturesDone++;

            // Encode ring buffer immediately
            EncodeCurrentRingBuffer();

            // Start streaming phase
            isStreaming = true;
            recordingSegmentStartTime = Time.time;
            streamEndTime = Time.time + streamDurationSeconds;
            framesCapturedInSegment = 0;

            if (!isRecording)
            {
                StartRecording();
            }
        }

        private void EncodeCurrentRingBuffer()
        {
            string sessionId = PerformanceProfiler.StartSession("Encode_Ring_Buffer", $"Frames: {frameRingBuffer.Count}");

            try
            {
                var (encoder, encoderCreateTime) = PerformanceProfiler.TimeOperation("Create_FrameSequenceEncoder", () => {
                    return new FrameSequenceEncoder(blockSize, colorQuantization, compressionLevel);
                }, $"BlockSize: {blockSize}, Quantization: {colorQuantization}, Compression: {compressionLevel}");

                var (payload, encodeTime) = PerformanceProfiler.TimeOperation("Encode_Sequence", () => {
                    return encoder.Encode(frameRingBuffer);
                }, $"Frames: {frameRingBuffer.Count}");

                if (payload != null)
                {
                    Debug.Log($"[ScreenRecorderController] Encoded & compressed payload size: {payload.Length} bytes.");

                    PerformanceProfiler.TimeOperation("Enqueue_Payload", () => {
                        VideoLogHandler.Instance?.EnqueuePayload(payload);
                    }, $"Size: {payload.Length} bytes");
                }

                PerformanceProfiler.TimeOperation("Clear_Ring_Buffer", () => {
                    frameRingBuffer.Clear(); // Clear after encoding to avoid duplicates
                });

                PerformanceProfiler.EndSession(sessionId, $"Success - {payload?.Length ?? 0} bytes");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ScreenRecorderController] Failed to encode/compress frames: {ex.Message}");
                PerformanceProfiler.EndSession(sessionId, $"Exception: {ex.Message}");
            }
        }

        private IEnumerator RestartAfterCooldown()
        {
            if (activationIntervalBetweenRecordingsSeconds > 0)
            {
                yield return new WaitForSeconds(activationIntervalBetweenRecordingsSeconds);
            }

            if (this != null && enabled)
            {
                frameRingBuffer?.Clear();
                StartRecording();
            }
        }

        /// <summary>
        /// Logs current performance statistics for all screen recording operations.
        /// Call this method to see timing information for hotspot analysis.
        /// </summary>
        public void LogPerformanceStatistics()
        {
            PerformanceProfiler.LogStatistics();
        }

        /// <summary>
        /// Clears all performance statistics.
        /// </summary>
        public void ClearPerformanceStatistics()
        {
            PerformanceProfiler.ClearStatistics();
        }

        void OnDestroy()
        {
            StopRecording(); // Ensure coroutine is stopped

            rt1?.Release();
            rt2?.Release();
            Destroy(rt1);
            Destroy(rt2);
            rt1 = null;
            rt2 = null;

            frameRingBuffer?.Dispose();
            frameRingBuffer = null;

            // Unsubscribe from scene change event
            SceneManager.activeSceneChanged -= OnActiveSceneChanged;

            if (captureCommandBuffer != null)
            {
                // Ensure it's removed from camera if not already
                if (targetCamera != null)
                {
                    targetCamera.RemoveCommandBuffer(CameraEvent.AfterSkybox, captureCommandBuffer);
                }
                captureCommandBuffer.Release(); // Release unmanaged resources
                captureCommandBuffer = null;
            }

            // Log final performance statistics before destruction
            LogPerformanceStatistics();
        }
    }
}