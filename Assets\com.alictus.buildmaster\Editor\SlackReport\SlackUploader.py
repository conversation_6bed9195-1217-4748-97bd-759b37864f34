import sys
import requests
import re
 
def send_post_request(url, bearer_token, build_number, message) -> None:
    print(f"Sending \"{message}\" for build \"{build_number}\"")

    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"  # Adjust if necessary
    }
    payload = {
        "build_num": f"{build_number}",
        "message": f"{message}"
    }

    try: 
        requests.post(url, json=payload, headers=headers, timeout=(None, 0.0000000001))
    except requests.exceptions.ReadTimeout:
        pass
 

def check_adressables_build_success(unity_log_file_path:str) -> bool: 
    with open(unity_log_file_path, 'r') as file:  
        logs = file.read()
        return "Addressable content successfully built" in logs
    

def write_adressables_build_status(unity_log_file_path:str) -> str:  
    success_message = " • :white_check_mark: Addressable content uploaded\n"
    failure_message = " • :x: Addressable content upload failed\n"
 
    success = check_adressables_build_success(unity_log_file_path)

    if success:
        return success_message
    else:
        return failure_message
        
    
def write_build_report_invalid_normals(unity_log_file_path:str) -> str:  
    invalid_normal_regex = "The mesh ([A-Za-z]+) has invalid normals"

    matched_lines = set()
    compiled_pattern = re.compile(invalid_normal_regex)

    with open(unity_log_file_path, 'r') as file:  
        lines = file.readlines() 
        for line in lines:
            result = compiled_pattern.search(line)
            if result:  # Check if the regex matches
                matched_lines.add(result.group(1))

    has_invalid_normals = len(matched_lines) > 0

    if has_invalid_normals: 
        message = " • :x: Following meshes contains invalid normals:\n"
        i = 1
        for line in matched_lines:
            message += f"\t{i}. {line}\n"
            i += 1
        return message
    else:
        return " • :white_check_mark: Build does not contain meshes with invalid normals\n"
    
    
def write_build_report_self_intersecting(unity_log_file_path:str) -> str:   
    self_intersecting_regex =  "A polygon of Mesh '([^']+)' in (Assets\/[^ ]+) is self-intersecting and has been discarded" 

    matched_lines = set()
    compiled_pattern = re.compile(self_intersecting_regex)

    with open(unity_log_file_path, 'r') as file:  
        lines = file.readlines() 
        for line in lines:
            result = compiled_pattern.search(line)
            if result: 
                mesh_name = result.group(1)
                mesh_path = result.group(2)
                matched_lines.add(f"{mesh_name}: {mesh_path}")
        
    has_self_intersecting_meshes = len(matched_lines) > 0

    if has_self_intersecting_meshes: 
        message = " • :x: Following meshes are self-intersecting:\n"
        i = 1
        for line in matched_lines:
            message += f"\t{i}. {line}\n" 
            i += 1
        return message
    else:
        return " • :white_check_mark: Build does not contain self-intersecting meshes\n"
    

def main():
    script_path = sys.argv[0]
    bearer_token = sys.argv[1]
    url = sys.argv[2]
    build_number = sys.argv[3]
    unity_log_file_path = sys.argv[4]
    
    message = write_adressables_build_status(unity_log_file_path)
    message += write_build_report_invalid_normals(unity_log_file_path)
    message += write_build_report_self_intersecting(unity_log_file_path) 
    send_post_request(url, bearer_token, build_number, message)

if __name__ == "__main__":
    main()
