﻿/*
using System;
using UnityEngine;

namespace com.alictus.common
{
    public class SessionData : ISessionData
    {
        public string PlayerId { get; set; }
        public string PlayfabId { get; set; }
        public string SessionId { get; set; }
        public string DeviceId { get; set; }
        public string Identifier { get; set; }
        public string Platform { get; set; }
        public int PlatformId { get; set; }
        public string DeviceModel { get; set; }
        public string EngineVersion { get; set; }
        public string GameVersion { get; set; }
        public string CountryCode { get; set; }
        public bool IsFirstSession { get; set; }
        private int? _buildNumberOverride;
        public int BuildNumber {
            get {
#if UNITY_IOS
            var computed = int.Parse(_IOS_BUILD_NUMBER());
#elif UNITY_ANDROID
            var computed = GetBuildBuildNumberAndroid();
#else
        var computed = 1;
#endif
                return _buildNumberOverride ?? computed;
            }
            set {
                _buildNumberOverride = value;
            }
        }
        public string OSVersion { get; set; }
        public bool IsInternalUser { get; set; }
        public double Timestamp { get; set; }

        #if UNITY_ANDROID        
        private static int GetBuildBuildNumberAndroid()
        {
            int getBuildNumber(AndroidJavaClass actClass)
            {
                var activity = actClass.GetStatic<AndroidJavaObject>("currentActivity");
                var packageManager = activity.Call<AndroidJavaObject>("getPackageManager");

                var packageName = activity.Call<string>("getPackageName");
                const int flags = 0;

                var packageInfo = packageManager.Call<AndroidJavaObject>("getPackageInfo", packageName, flags);
                return (int)packageInfo.Call<long>("getLongVersionCode");
            }
            
            try
            {
                using var actClass = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                return getBuildNumber(actClass);
            }
            catch (Exception e)
            {
                try
                {
                    using var actClass = new AndroidJavaClass("com.firebase.MessagingUnityPlayerActivity");
                    return getBuildNumber(actClass);
                }
                catch (Exception exception)
                {
                    return 1; // Return -1 if unable to retrieve build number
                }
            }
        }
        #endif
    }
}
*/
