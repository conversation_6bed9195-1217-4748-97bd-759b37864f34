﻿using System;
using UnityEngine;

namespace com.alictus.common
{
    public class Utilty
    {
        public static string GetPlatformString()
        {
            #if UNITY_IOS
                            return "ios";
            #elif UNITY_ANDROID
                        return "android";
            #endif
                        return "unknown";
        }
        
        public static int GetPlatformId()
        {
            #if UNITY_IOS
                return 1;
            #elif UNITY_ANDROID
                return 2;
            #endif
                return 0;
        }
        
        public static double GetUtcTimestamp()
        {
            DateTime utcNow = DateTime.UtcNow;
            DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            TimeSpan timeSpan = utcNow - epoch;
            double timestamp = timeSpan.TotalSeconds;
            return timestamp;
        }
        
#if UNITY_ANDROID        
        public static int GetBuildBuildNumberAndroid()
        {
            int getBuildNumber(AndroidJavaClass actClass)
            {
                var activity = actClass.GetStatic<AndroidJavaObject>("currentActivity");
                var packageManager = activity.Call<AndroidJavaObject>("getPackageManager");

                var packageName = activity.Call<string>("getPackageName");
                const int flags = 0;

                var packageInfo = packageManager.Call<AndroidJavaObject>("getPackageInfo", packageName, flags);
                return (int)packageInfo.Call<long>("getLongVersionCode");
            }
            
            try
            {
                using var actClass = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                return getBuildNumber(actClass);
            }
            catch (Exception e)
            {
                try
                {
                    using var actClass = new AndroidJavaClass("com.firebase.MessagingUnityPlayerActivity");
                    return getBuildNumber(actClass);
                }
                catch (Exception exception)
                {
                    return 1; // Return -1 if unable to retrieve build number
                }
            }
        }
#endif
    }
}