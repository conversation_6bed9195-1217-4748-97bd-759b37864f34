using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace com.alictus.buildmaster.Editor.Utilities
{
    public static class BuildBackendUtilities
    {
        internal class BackendBuildNumberResponse
        {
            public string bundle_id;
            public int build_number;
        }
 
        public static int GetBuildNumber()
        {
            //Retry to request buildnumber, raise exception afterwards
            const int requestCount = 4;
            const int requestWaitTime = 1500;

            for (int i = 0; i < requestCount; i++)
            {
                if (TryGetBuildNumber(out var buildNumber))
                {
                    return buildNumber;
                }
                BuildLogger.LogInfo("Failed to get build number, retrying");
                Thread.Sleep(requestWaitTime);
            }
            throw new Exception("Failed to get build number");
        }
        private static bool TryGetBuildNumber(out int buildNumber)
        {
            try
            {
                using var webClient = new WebClient();
                webClient.Headers.Add("X-Auth-Token", "yoxJUgAa6s9PwFPZZcXDd1oYrtSrx6vd");
                webClient.QueryString.Add("bundleId", Application.identifier);
#if UNITY_IOS
                webClient.QueryString.Add("platform", "ios");
#elif UNITY_ANDROID
                webClient.QueryString.Add("platform", "android");
#endif

                BuildLogger.LogInfo($"Trying to get build number for {Application.identifier}");

                var response = webClient.DownloadString("https://build-backend.alictuscloud.com/getBuildNumber"); 
                var parsedResponse = JsonUtility.FromJson<BackendBuildNumberResponse>(response);
                if (parsedResponse == null)
                {
                    throw new Exception($"Failed to parse build number. Server response: {response}");
                }
                BuildLogger.LogInfo($"Backend Build number: {parsedResponse.build_number}");
                buildNumber = parsedResponse.build_number;
                return true;
            }
            catch (WebException we)
            {
                var response = (HttpWebResponse)we.Response;
                var errorMsg = new StringBuilder();
                errorMsg.AppendLine("WebException raised.");
                errorMsg.Append("Message: ").AppendLine(we.Message);
                errorMsg.Append("StatusCode: ").AppendLine(response.StatusCode.ToString());
                errorMsg.Append("StatusDescription: ").AppendLine(response.StatusDescription);
                var stream = response.GetResponseStream();
                if (stream != null)
                {
                    var resp = new StreamReader(stream).ReadToEnd();
                    if (!string.IsNullOrEmpty(resp))
                    {
                        errorMsg.Append("Server response: ").AppendLine(resp);
                    }
                }
                BuildLogger.LogError(errorMsg.ToString());
            }
            catch (Exception e)
            {
                BuildLogger.LogError($"Exception raised. Message: {e.Message}");
            }
            buildNumber = -1;
            return false;
        }
    }
}