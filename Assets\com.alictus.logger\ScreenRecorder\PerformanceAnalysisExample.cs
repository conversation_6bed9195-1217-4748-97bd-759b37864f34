using UnityEngine;
using ScreenRecording;

namespace ScreenRecording
{
    /// <summary>
    /// Example script demonstrating how to use the performance profiling features
    /// for analyzing screen recording hotspots.
    /// </summary>
    public class PerformanceAnalysisExample : MonoBehaviour
    {
        [Header("Performance Analysis")]
        [SerializeField] private bool enablePerformanceLogging = true;
        [SerializeField] private float statisticsLogInterval = 10f; // Log stats every 10 seconds
        
        private ScreenRecorderController screenRecorderController;
        private float lastStatsLogTime;
        
        void Start()
        {
            // Find the screen recorder controller
            screenRecorderController = FindObjectOfType<ScreenRecorderController>();
            
            if (screenRecorderController == null)
            {
                Debug.LogWarning("[PerformanceAnalysisExample] No ScreenRecorderController found in scene.");
                return;
            }
            
            if (enablePerformanceLogging)
            {
                Debug.Log("[PerformanceAnalysisExample] Performance profiling enabled. Statistics will be logged every " + statisticsLogInterval + " seconds.");
                lastStatsLogTime = Time.time;
            }
        }
        
        void Update()
        {
            if (!enablePerformanceLogging || screenRecorderController == null)
                return;
                
            // Log performance statistics at regular intervals
            if (Time.time - lastStatsLogTime >= statisticsLogInterval)
            {
                LogCurrentPerformanceStats();
                lastStatsLogTime = Time.time;
            }
        }
        
        /// <summary>
        /// Logs current performance statistics to help identify hotspots.
        /// </summary>
        [ContextMenu("Log Performance Statistics")]
        public void LogCurrentPerformanceStats()
        {
            Debug.Log("=== SCREEN RECORDER PERFORMANCE ANALYSIS ===");
            
            if (screenRecorderController != null)
            {
                screenRecorderController.LogPerformanceStatistics();
            }
            else
            {
                PerformanceProfiler.LogStatistics();
            }
            
            Debug.Log("=== END PERFORMANCE ANALYSIS ===");
        }
        
        /// <summary>
        /// Clears all performance statistics to start fresh analysis.
        /// </summary>
        [ContextMenu("Clear Performance Statistics")]
        public void ClearPerformanceStats()
        {
            if (screenRecorderController != null)
            {
                screenRecorderController.ClearPerformanceStatistics();
            }
            else
            {
                PerformanceProfiler.ClearStatistics();
            }
            
            Debug.Log("[PerformanceAnalysisExample] Performance statistics cleared.");
        }
        
        /// <summary>
        /// Provides analysis tips for interpreting the performance data.
        /// </summary>
        [ContextMenu("Show Performance Analysis Tips")]
        public void ShowAnalysisTips()
        {
            Debug.Log(@"=== PERFORMANCE ANALYSIS TIPS ===

KEY OPERATIONS TO MONITOR:
1. Frame_Capture_Cycle - Overall frame capture performance
2. GPU_Readback_Complete - GPU to CPU data transfer time
3. Frame_Encode - Individual frame encoding time
4. Frame_Diffing - Block comparison performance (major hotspot)
5. Sequence_Encode - Full sequence encoding time
6. Data_Compression - Compression performance

HOTSPOT IDENTIFICATION:
- Look for operations with high Average times
- Check operations with high Max times (spikes)
- Monitor operations with high Total times (cumulative impact)

OPTIMIZATION TARGETS:
- Frame_Diffing: Consider larger block sizes or lower quantization
- GPU_Readback_Complete: May indicate GPU/CPU sync issues
- Data_Compression: Consider lower compression levels
- Buffer operations: Usually fast, high times indicate memory issues

PERFORMANCE IMPACT:
- Frame_Capture_Cycle should be < 16.67ms for 60fps
- GPU_Readback_Complete spikes indicate frame drops
- High Frame_Encode times suggest encoding bottleneck

=== END ANALYSIS TIPS ===");
        }
        
        void OnGUI()
        {
            if (!enablePerformanceLogging)
                return;
                
            // Simple on-screen performance monitoring
            GUILayout.BeginArea(new Rect(10, 10, 300, 100));
            GUILayout.BeginVertical("box");
            
            GUILayout.Label("Screen Recorder Performance Monitor");
            
            if (GUILayout.Button("Log Performance Stats"))
            {
                LogCurrentPerformanceStats();
            }
            
            if (GUILayout.Button("Clear Performance Stats"))
            {
                ClearPerformanceStats();
            }
            
            if (GUILayout.Button("Show Analysis Tips"))
            {
                ShowAnalysisTips();
            }
            
            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }
}
