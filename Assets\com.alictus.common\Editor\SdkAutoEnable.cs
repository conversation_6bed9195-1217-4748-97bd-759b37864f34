using System;
using UnityEditor;
using UnityEngine;

namespace Alictus.Common.Editor
{
    [InitializeOnLoad]
    public static class SdkAutoEnable
    {
        private const string SessionKey = "AlictusSDK_AutoEnabled";

        static SdkAutoEnable()
        {
            // Only run once per session
            if (SessionState.GetBool(SessionKey, false)) return;

            var env = Environment.GetEnvironmentVariable("SDK_ENABLED");
            if (IsTruthy(env))
            {
                Debug.Log("[SdkAutoEnable] SDK_ENABLED detected. Enabling SDK upfront.");
                SdkManager.EnableSdk();
                SessionState.SetBool(SessionKey, true);
            }
        }

        private static bool IsTruthy(string value)
        {
            if (string.IsNullOrEmpty(value)) return false;
            value = value.Trim().ToLowerInvariant();
            return value == "1" || value == "true" || value == "yes";
        }
    }
}
