using System.IO; 
using UnityEditor.Build;
using UnityEditor.Build.Reporting;

namespace com.alictus.buildmaster.Editor.BuildJobs
{
    public class CheckBuildDataDirectoryStep : IPreprocessBuildWithReport
    {
        public int callbackOrder => (int)PreBuildJobType.CheckBuildDataDirectory;
        public void OnPreprocessBuild(BuildReport report) => Execute();

        public static void Execute()
        {
            if (Directory.Exists(BuildConstants.DATA_DIRECTORY))
            { 
                BuildLogger.LogInfo($"Build data directory exists.");
                return;
            }

            BuildLogger.LogInfo($"Creating build data directory. ");
            Directory.CreateDirectory(BuildConstants.DATA_DIRECTORY);
        }
    }
}