using System;
using System.Diagnostics;
using System.IO;
using NativeCompression;
using Debug = UnityEngine.Debug;

namespace ScreenRecording
{
    public class FrameSequenceEncoder
    {
        private EncodedSequence _encodedSequence = new EncodedSequence();
        private int blockSize;
        private int colorQuantization;
        private int compressionLevel;
        
        public FrameSequenceEncoder(int blockSize, int colorQuantization, int compressionLevel = 3)
        {
            this.blockSize = blockSize;
            this.colorQuantization = colorQuantization;
            this.compressionLevel = compressionLevel;
        }
        
        // Returns the compressed byte array representing the encoded frame sequence.
        // If encoding or compression fails, returns null.
        public byte[] Encode(FrameRingBuffer inputRingBuffer, RawFrameData lastEncodedRawFrame = null)
        {
            string sequenceSessionId = PerformanceProfiler.StartSession("Sequence_Encode", $"Frames: {inputRingBuffer?.Count ?? 0}");

            try
            {
                if (inputRingBuffer == null)
                {
                    Debug.LogError("Cannot encode ring buffer: Not initialized.");
                    PerformanceProfiler.EndSession(sequenceSessionId, "Error - null input");
                    return null;
                }

                Debug.Log($"Error occurred. Encoding {inputRingBuffer.Count} frames from ring buffer.");

                int frameCount = 0;
                PerformanceProfiler.TimeOperation("Frame_Iteration_And_Encoding", () => {
                    foreach (var rawFrame in inputRingBuffer.GetFramesChronological())
                    {
                        if (rawFrame == null) continue; // Should not happen with pre-allocated buffer

                        EncodedFrame encoded = FrameEncoder.EncodeFrame(rawFrame, lastEncodedRawFrame, blockSize, colorQuantization);
                        _encodedSequence.AddFrame(encoded);
                        // The raw frame from the ring buffer becomes the reference for the *next* frame from the ring buffer.
                        // We don't "own" this rawFrame, so we can't modify it or keep it long-term.
                        // For diffing within this batch, we need a copy if FrameEncoder modifies its input,
                        // or ensure FrameEncoder treats previousRawFrame as read-only.
                        // Our current FrameEncoder reads from previousRawFrame but doesn't modify it.
                        lastEncodedRawFrame = rawFrame; // This is okay as long as FrameEncoder doesn't hold onto it.
                        frameCount++;
                    }
                }, $"Processed {frameCount} frames");

                Debug.Log($"Encoded {_encodedSequence.Frames.Count} frames. Total duration: {CalculateTotalEncodingTime(_encodedSequence)}ms");

                var (serializedSequence, serializationTime) = PerformanceProfiler.TimeOperation("Sequence_Serialization", () => {
                    return _encodedSequence.Serialize();
                }, $"Frames: {_encodedSequence.Frames.Count}");

                Debug.Log($"Serialized sequence size: {serializedSequence.Length} bytes");

                var compressionResult = Compress(serializedSequence, compressionLevel);
                if (compressionResult.Status == CompressionStatus.Success)
                {
                    Debug.Log($"Compression successful. Compressed size: {compressionResult.Data.Length} bytes");

                    /*try
                    {
                        string fileName = $"video_{DateTime.Now:yyyyMMdd_HHmmss}.bin";
                        File.WriteAllBytes(fileName, compressionResult.Data);
                        Debug.Log($"Compressed video saved to: {Path.GetFullPath(fileName)}");
                    }
                    catch (Exception ex) // Catch generic Exception for broader file IO issues
                    {
                        Debug.LogError($"Failed to save video file: {ex.Message}");
                    }*/

                    PerformanceProfiler.EndSession(sequenceSessionId,
                        $"Success - {compressionResult.Data.Length} bytes compressed from {serializedSequence.Length} bytes");

                    // Return the compressed payload so callers can dispatch / store it.
                    return compressionResult.Data;
                }

                // Compression failed
                Debug.LogError("FrameSequenceEncoder: Compression failed.");
                PerformanceProfiler.EndSession(sequenceSessionId, "Error - compression failed");
                return null;
            }
            catch (Exception ex)
            {
                PerformanceProfiler.EndSession(sequenceSessionId, $"Exception: {ex.Message}");
                throw;
            }
        }
        
        private CompressionResult Compress(byte[] data, int compressionLevel=3)
        {
            var (compressionResult, compressionTime) = PerformanceProfiler.TimeOperation("Data_Compression", () => {
                return CompressionManager.Compress(data, compressionLevel);
            }, $"Level: {compressionLevel}, Input: {data.Length} bytes");

            float compressionRatio = ((float)compressionResult.Data.Length / data.Length) * 100;
            Debug.Log($"Compressed sequence size with level {compressionLevel} : {compressionResult.Data.Length} bytes with performance: {compressionTime:F2}ms and compression ratio: {compressionRatio:F1}%");

            return compressionResult;
        }
        
        private float CalculateTotalEncodingTime(EncodedSequence sequence)
        {
            float total = 0;
            foreach(var frame in sequence.Frames)
            {
                total += frame.EncodingDurationMs;
            }
            return total;
        }

    }
}