﻿using System;
using System.Text;
using System.Text.RegularExpressions;
using com.alictus.common;
using UnityEngine;

namespace com.alictus.logger.Common
{
    public static class Utility
    {
        public static NativeLogType GetLogType(LogType type)
        {
            return type switch
            {
                LogType.Log => NativeLogType.Debug,
                LogType.Warning => NativeLogType.Warning,
                LogType.Error => NativeLogType.Error,
                LogType.Exception => NativeLogType.Exception,
                LogType.Assert => NativeLogType.Assert,
                _ => NativeLogType.Unknown
            };
        }
        
        public static double GetUtcTimestamp()
        {
            DateTime utcNow = DateTime.UtcNow;
            DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            TimeSpan timeSpan = utcNow - epoch;
            double timestamp = timeSpan.TotalSeconds;
            return timestamp;
        }
        
        public static class UrlFormatter
        {
            public static bool TryResolveUrl(ISessionProvider sessionProvider, string urlTemplate, out string resolvedUrl)
            {
                resolvedUrl = urlTemplate; // Default to template if no processing needed or on failure
                if (string.IsNullOrEmpty(urlTemplate))
                {
                    return true; // Or false if an empty template is an error
                }
    
                var regex = new Regex(@"\$([a-zA-Z0-9_]+)"); // Finds $VariableName
                var matches = regex.Matches(urlTemplate);
    
                if (matches.Count == 0)
                {
                    return true; // No placeholders, template is already resolved
                }
    
                var tempUrlBuilder = new StringBuilder(urlTemplate);
                bool allPlaceholdersReplaced = true;
    
                foreach (Match match in matches)
                {
                    string placeholderWithDollar = match.Value; // e.g., "$PlayerId"
                    string key = match.Groups[1].Value;      // e.g., "PlayerId"
    
                    if (sessionProvider.TryGetValue(key, out string value))
                    {
                        tempUrlBuilder.Replace(placeholderWithDollar, value);
                    }
                    else
                    {
                        // Variable not found in MetaDataRegistry
                        allPlaceholdersReplaced = false;
                        break; // Stop processing, as we need to skip if any variable is missing
                    }
                }
    
                if (allPlaceholdersReplaced)
                {
                    resolvedUrl = tempUrlBuilder.ToString();
                    return true;
                }
                
                return false; // Indicates failure to resolve, so the caller can skip
                }
            }
            
            public static Platform GetPlatform()
            {
                switch (Application.platform)
                {
                    case RuntimePlatform.Android:
                        return Platform.Android;
                    case RuntimePlatform.IPhonePlayer:
                        return Platform.iOS;
                    default:
                        return Platform.Editor;
                }
            }
            
            public static string GetPlatformString()
            {
                return GetPlatform().ToString().ToLowerInvariant();
            }
    }
    
    public static class SerializationHelpers
    {
        public static byte[] ValueToByteArray(object value, KeyType type)
        {
            try {
                switch (type)
                {
                    case KeyType.Null: // Handle Null type
                        return Array.Empty<byte>();
                    case KeyType.String:
                        return Encoding.UTF8.GetBytes((string)value ?? string.Empty); // Should not be null if type is String
                    case KeyType.ByteArray: // Handle byte[] explicitly
                        var bytes = (byte[])value; // Should not be null if type is ByteArray
                        if (bytes == null) return Array.Empty<byte>(); // Defensive, GetKeyType should prevent this path for non-null type
                        var copy = new byte[bytes.Length];
                        Array.Copy(bytes, copy, bytes.Length);
                        return copy;
                    case KeyType.Int:
                        return BitConverter.GetBytes((int)value);
                    case KeyType.Long:
                        return BitConverter.GetBytes((long)value);
                    case KeyType.Float:
                        return BitConverter.GetBytes((float)value);
                    case KeyType.Double:
                        return BitConverter.GetBytes((double)value);
                    case KeyType.Bool:
                        return BitConverter.GetBytes((bool)value);
                    case KeyType.Short:
                        return BitConverter.GetBytes((short)value);
                    case KeyType.Byte: // Handle single byte
                        return new byte[] { (byte)value };
                    default:
                        UnityEngine.Debug.LogError($"Unsupported KeyType for serialization: {type}");
                        throw new ArgumentException("Unsupported value type for serialization");
                }
            } catch (InvalidCastException ex) {
                UnityEngine.Debug.LogError($"Failed to cast value to expected type {type}. Value: '{value}'. Error: {ex.Message}");
                throw; // Rethrow
            } catch (Exception ex) {
                 UnityEngine.Debug.LogError($"Error serializing value of type {type}. Value: '{value}'. Error: {ex.Message}");
                 throw; // Rethrow
            }
        }

        public static KeyType GetKeyType(object value)
        {
            if (value == null)
            {
                return KeyType.Null; // Return Null type for null values
            }
            switch (value)
            {
                case string _:
                    return KeyType.String;
                case byte _:
                    return KeyType.Byte;
                case byte[] _:
                    return KeyType.ByteArray;
                case int _:
                    return KeyType.Int;
                case long _:
                    return KeyType.Long;
                case float _:
                    return KeyType.Float;
                case double _:
                    return KeyType.Double;
                case bool _:
                    return KeyType.Bool;
                case short _:
                    return KeyType.Short;
                default:
                    throw new ArgumentException($"Unsupported value type: {value.GetType()}");
            }
        }
   
        public static object DeserializeValue(byte[] data, KeyType type)
        {
            try {
                switch (type)
                {
                    case KeyType.Null: // Handle Null type
                        return null;
                    case KeyType.String:
                        if (data == null) return null; // Or string.Empty, depending on desired behavior for null byte array
                        return Encoding.UTF8.GetString(data);
                    case KeyType.ByteArray:
                        return data; // data can be null here if it was serialized as such
                    case KeyType.Int:
                        if (data == null || data.Length < sizeof(int)) throw new ArgumentException($"Data null or too short ({data?.Length ?? 0} bytes) for Int");
                        return BitConverter.ToInt32(data, 0);
                    case KeyType.Long:
                         if (data == null || data.Length < sizeof(long)) throw new ArgumentException($"Data null or too short ({data?.Length ?? 0} bytes) for Long");
                        return BitConverter.ToInt64(data, 0);
                    case KeyType.Float:
                         if (data == null || data.Length < sizeof(float)) throw new ArgumentException($"Data null or too short ({data?.Length ?? 0} bytes) for Float");
                        return BitConverter.ToSingle(data, 0);
                    case KeyType.Double:
                         if (data == null || data.Length < sizeof(double)) throw new ArgumentException($"Data null or too short ({data?.Length ?? 0} bytes) for Double");
                        return BitConverter.ToDouble(data, 0);
                    case KeyType.Bool:
                         if (data == null || data.Length < sizeof(bool)) throw new ArgumentException($"Data null or too short ({data?.Length ?? 0} bytes) for Bool");
                        return BitConverter.ToBoolean(data, 0);
                    case KeyType.Short:
                         if (data == null || data.Length < sizeof(short)) throw new ArgumentException($"Data null or too short ({data?.Length ?? 0} bytes) for Short");
                        return BitConverter.ToInt16(data, 0);
                    case KeyType.Byte:
                         if (data == null || data.Length < 1) throw new ArgumentException($"Data null or too short ({data?.Length ?? 0} bytes) for Byte");
                         return data[0];
                    default:
                         UnityEngine.Debug.LogError($"Unsupported KeyType for deserialization: {type}");
                        throw new ArgumentException("Unsupported value type for deserialization");
                }
            } catch (Exception ex) {
                 UnityEngine.Debug.LogError($"Error deserializing value of type {type}. Data Length: {data?.Length ?? 0}. Error: {ex.Message}");
                 throw; // Rethrow
            }
        }
    }
}