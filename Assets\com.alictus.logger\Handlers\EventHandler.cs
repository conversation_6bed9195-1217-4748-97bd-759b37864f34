﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading;
using com.alictus.common;
using com.alictus.common.interfaces;
using com.alictus.logger.Common;
using Cysharp.Threading.Tasks;
using Debug = UnityEngine.Debug;

namespace com.alictus.logger.runtime
{
    public class EventHandler : IHandler
    {
        private static readonly ConcurrentQueue<ILogBase> EventQueue = new ConcurrentQueue<ILogBase>();
        private OfflineLogStore _offlineLogStore;
        private CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
        private int _estimatedQueueSize;
        private LoggerConfig _logConfig;
        
        public void Initialize(LoggerConfig logConfig)
        {
            if(!logConfig.EventConfig.Enabled)
            {
                Debug.Log("[Logger] EventHandler is not enabled. Initialization skipped.");
                return;
            }

            _logConfig = logConfig;
            _logConfig.sdkProvider.AddListener("InternalAnalytic_EventOccuredForAllPlatforms", ProcessGaEvents);
            _logConfig.sdkProvider.AddListener("InternalAnalytic_EventOccuredForPlatform_GAME_ANALYTICS", ProcessGaEvents);
            
            if(_logConfig.EventConfig.PersistenceConfig.Enabled)
            {
                _offlineLogStore = new OfflineLogStore();
                _offlineLogStore.Init(_logConfig.EventConfig.PersistenceConfig);
            }
            
            UniTask.RunOnThreadPool(() => StartProcessing(_cancellationTokenSource.Token));
        }
        
        private void ProcessGaEvents(object parametersAsObj)
        {
            if (parametersAsObj == null)
                return;

            Tuple<string, Dictionary<string, object>> tuple =
                (Tuple<string, Dictionary<string, object>>) parametersAsObj;

            Dictionary<string, object> paramsDict = tuple.Item2;
            try
            {
                ForwardGaEvents(tuple.Item1, paramsDict);
            }
            catch (Exception e)
            {
                Debug.LogError("Exception Occured While Sending the Event: " + e);
            }
        }

        private void ForwardGaEvents(string eventName, Dictionary<string, object> parameters)
        {
            if (parameters == null)
            {
                parameters = new Dictionary<string, object>();
            }

            parameters["event_name"] = eventName;
            parameters["Country"] = _logConfig.sessionProvider.CountryCode;
           
            try
            {
                CreateSdkLog(eventName, parameters);
            }
            catch (Exception e)
            {
                Debug.Log(e);
            }
        }

        
        private void CreateSdkLog(string eventName, Dictionary<string, object> eventParameters)
        {
            var eventData = new EventData
            {
                SessionId = _logConfig.sessionProvider.SessionId,
                EventName = eventName,
                EventNumber = EventNumberProvider.GetNextEventNumber(),
                Timestamp = Utility.GetUtcTimestamp(), 
                Data = eventParameters,
            };
            
            EventQueue.Enqueue(eventData); 
        }
        
        private async UniTask StartProcessing(CancellationToken token)
        {
            var  lastDispatchTime = new Stopwatch();
            var eventConfig = _logConfig.EventConfig;
            while (!token.IsCancellationRequested)
            {
                try
                {
                    await UniTask.DelayFrame(20, cancellationToken: token);
                    token.ThrowIfCancellationRequested();
                    if (EventQueue.Count >= eventConfig.BatchingConfig.MaxEventsPerBatch ||
                        _estimatedQueueSize >= eventConfig.BatchingConfig.MaxUncompressedSize ||
                        eventConfig.BatchingConfig.GetDispatchInterval() < lastDispatchTime.ElapsedMilliseconds
                       )
                    {
                        Debug.Log("Processing log events batch.");
                        var serializedData = SerializeBatch();
                        if (serializedData != null && serializedData.Length > 0)
                        {
                            var webReq = WebRequestManager.SendRequest(
                                eventConfig.ConnectionConfig.Endpoint,
                                eventConfig.ConnectionConfig.MaxRetries,
                                eventConfig.ConnectionConfig.Timeout,
                                serializedData);
                            
                            webReq.OnComplete += OnWebRequestComplete;
                            Debug.Log("Serialized data sent successfully.");
                        }
                        else
                        {
                            Debug.LogWarning("Serialized data is null or empty.");
                        }
                        
                        _estimatedQueueSize = 0;
                        lastDispatchTime.Restart();
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Error processing log: {e}");
                }
            }
        }
        
        private void OnWebRequestComplete(ServerConnection req)
        {
            if (req.CurrentStatus == RequestStatus.Success)
            {
                Debug.Log("Web request completed successfully.");
            }
            else
            {
                if (!_logConfig.EventConfig.PersistenceConfig.Enabled) return;
                Debug.LogError($"Web request failed with status: {req.CurrentStatus}");
                _offlineLogStore.SaveFailedRequest(req.GetEndpoint(), req.GetPayload());
            }
        }

        public void FlushToDisk()
        {
            var serializedQueue = SerializeQueue(int.MaxValue); // Serialize all events
            if (serializedQueue == null || serializedQueue.Length == 0)
            {
                Debug.LogWarning("Serialized queue is null or empty. Nothing to flush.");
                return;
            }
            
            _offlineLogStore.SaveFailedRequest(_logConfig.EventConfig.ConnectionConfig.Endpoint, serializedQueue);
            _offlineLogStore.Save();
        }

        public byte[] SerializeBatch()
        {
            var serializedQueue = SerializeQueue(_logConfig.EventConfig.BatchingConfig.MaxEventsPerBatch);
            if (serializedQueue == null || serializedQueue.Length == 0)
            {
                Debug.LogWarning("Serialized queue is null or empty.");
                return new byte[] { };
            }
            
            Debug.Log($"Serialized batch size: {serializedQueue.Length} bytes");
            return serializedQueue;
        }

        public byte[] SerializeQueue(int batchSize)
        {
            if (EventQueue.Count == 0) 
            {
                Debug.Log("EventQueue is empty");
                return new byte[] { };
            }

            var messageDataCombined = new BinaryEventArrayData();
            int totalBytes = 0;
            int combinedEventCount = 0;
            
            try
            {
                while (EventQueue.Count > 0 && combinedEventCount < batchSize)
                {
                    using (var ms = new MemoryStream())
                    {
                        using (var logDataBinaryWriter = new BinaryWriter(ms))
                        {
                            EventQueue.TryDequeue(out var logData);
                            if (logData == null) continue;
                            logData.Serialize(logDataBinaryWriter);
                            var r = ms.ToArray();
                            messageDataCombined.Data.Add(r);
                            totalBytes += r.Length;
                            combinedEventCount++;
                        }
                    }
                }
                
                messageDataCombined.ArrayItemCount = combinedEventCount;
                messageDataCombined.IsCompressed = false;
                messageDataCombined.UncompressedSize = totalBytes;
                using (var ms = new MemoryStream())
                {
                    using (var logDataBinaryWriter = new BinaryWriter(ms))
                    {
                        messageDataCombined.Serialize(logDataBinaryWriter);
                        return ms.ToArray();
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError("Exception Occured While Sending the Event: " + e);
            }
            
            return new byte[] { };
        }
    }
}