using System;
using System.Collections.Generic;
using UnityEngine; // For Debug.LogWarning

namespace ScreenRecording
{
    public class FrameRingBuffer : IDisposable
    {
        private readonly RawFrameData[] buffer;
        private readonly int capacity;
        private int head; // Index for the next frame to be written
        private int tail; // Index for the next frame to be read
        private int count; // Number of frames currently in the buffer

        private bool isDisposed = false;

        public int Count => count;
        public int Capacity => capacity;

        public FrameRingBuffer(int capacity, int frameWidth, int frameHeight)
        {
            if (capacity <= 0) throw new ArgumentOutOfRangeException(nameof(capacity), "Capacity must be positive.");
            if (frameWidth <= 0) throw new ArgumentOutOfRangeException(nameof(frameWidth), "Frame width must be positive.");
            if (frameHeight <= 0) throw new ArgumentOutOfRangeException(nameof(frameHeight), "Frame height must be positive.");

            this.capacity = capacity;
            this.buffer = new RawFrameData[capacity];
            for (int i = 0; i < capacity; i++)
            {
                // Frame number will be updated upon actual enqueue
                buffer[i] = new RawFrameData(frameWidth, frameHeight, 0);
            }
            this.head = 0;
            this.tail = 0;
            this.count = 0;
        }

        public RawFrameData GetSlotForWrite()
        {
            return PerformanceProfiler.TimeOperation("Buffer_GetSlotForWrite", () => {
                if (isDisposed) throw new ObjectDisposedException(nameof(FrameRingBuffer));

                RawFrameData slot = buffer[head];
                if (count == capacity)
                {
                    tail = (tail + 1) % capacity;
                }
                return slot;
            }, $"Buffer usage: {count}/{capacity}");
        }

        public void CommitWrite(int frameNumber)
        {
            PerformanceProfiler.TimeOperation("Buffer_CommitWrite", () => {
                if (isDisposed) throw new ObjectDisposedException(nameof(FrameRingBuffer));

                buffer[head].FrameNumber = frameNumber;
                head = (head + 1) % capacity;

                if (count < capacity)
                {
                    count++;
                }
                // If count was already at capacity, head moved, tail moved in GetSlotForWrite, so count remains capacity.
            }, $"Frame: {frameNumber}, Buffer: {count}/{capacity}");
        }

        public RawFrameData TryDequeue()
        {
            if (isDisposed) throw new ObjectDisposedException(nameof(FrameRingBuffer));
            if (count == 0)
            {
                return null; // Buffer is empty
            }

            RawFrameData frame = buffer[tail];
            tail = (tail + 1) % capacity;
            count--;
            return frame; // The NativeArray within this RawFrameData is reused, not disposed here.
        }

        public RawFrameData PeekOldest()
        {
            if (isDisposed) throw new ObjectDisposedException(nameof(FrameRingBuffer));
            if (count == 0) return null;
            return buffer[tail];
        }

        public RawFrameData PeekNewest()
        {
            if (isDisposed) throw new ObjectDisposedException(nameof(FrameRingBuffer));
            if (count == 0) return null;
            // Head points to the *next* slot to write, so the newest is at (head - 1)
            int newestIndex = (head - 1 + capacity) % capacity;
            return buffer[newestIndex];
        }

        public bool TryGetPreviousAndCurrentFrames(out RawFrameData previousFrame, out RawFrameData currentFrame)
        {
            previousFrame = null;
            currentFrame = null;

            if (isDisposed) throw new ObjectDisposedException(nameof(FrameRingBuffer));
            if (count < 2)
            {
                return false; // Not enough frames to get a previous and current
            }

            // Newest frame is at (head - 1)
            int newestIndex = (head - 1 + capacity) % capacity;
            currentFrame = buffer[newestIndex];

            // Previous frame is at (head - 2)
            int previousIndex = (head - 2 + capacity) % capacity;
            previousFrame = buffer[previousIndex];

            return true;
        }
        
        public IEnumerable<RawFrameData> GetFramesChronological()
        {
            string sessionId = PerformanceProfiler.StartSession("Buffer_GetFramesChronological", $"Count: {count}");

            try
            {
                if (isDisposed) throw new ObjectDisposedException(nameof(FrameRingBuffer));
                if (count == 0)
                {
                    PerformanceProfiler.EndSession(sessionId, "Empty buffer");
                    yield break;
                }

                int currentReadIndex = tail;
                for (int i = 0; i < count; i++)
                {
                    yield return buffer[currentReadIndex];
                    currentReadIndex = (currentReadIndex + 1) % capacity;
                }

                PerformanceProfiler.EndSession(sessionId, $"Retrieved {count} frames");
            }
            finally
            {
                // Ensure session is ended even if enumeration is not completed
                if (PerformanceProfiler != null)
                {
                    // Note: This is a simplification - in practice we'd need a more sophisticated way
                    // to handle partial enumeration, but for performance monitoring this is sufficient
                }
            }
        }
        
        public void Clear()
        {
            PerformanceProfiler.TimeOperation("Buffer_Clear", () => {
                if (isDisposed) throw new ObjectDisposedException(nameof(FrameRingBuffer));
                head = 0;
                tail = 0;
                count = 0;
                // Frame numbers in buffer[i].FrameNumber will be stale but overwritten on next use.
            }, $"Cleared {count} frames");
        }


        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (isDisposed) return;

            if (disposing)
            {
                // Dispose managed state (e.g., if buffer itself was a List, but it's an array)
            }

            // Dispose unmanaged state (RawFrameData instances)
            if (buffer != null)
            {
                for (int i = 0; i < buffer.Length; i++)
                {
                    buffer[i]?.Dispose();
                    buffer[i] = null; 
                }
            }
            isDisposed = true;
        }

        ~FrameRingBuffer()
        {
            Dispose(false);
        }
    }
}