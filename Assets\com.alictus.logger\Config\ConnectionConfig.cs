using System;
using System.Collections.Generic;
using com.alictus.common;
using com.alictus.logger.Common;
using UnityEngine;

namespace com.alictus.logger.runtime
{
    public class ConnectionConfig : IConnectionConfig
    {
        public bool Enabled { get; set; } = true;
        public string Endpoint { get; set; } = "";
        public int Timeout { get; set; } = 10;
        public int MaxRetries { get; set; } = 3;
        public List<int> ExponentialBackoffMs { get; set; } = new List<int>(); // { 1000, 2000, 4000, 8000 }; // in ms
        public Dictionary<string, string> Headers { get; set; } = new Dictionary<string, string>();
        private int _exponentialBackoffIndex = 0;
        private ISessionProvider _sessionProvider;

        public ConnectionConfig(ISessionProvider sessionProvider)
        {
            _sessionProvider = sessionProvider;
        }
        
        public ConnectionConfig(ISessionProvider sessionProvider, string url, int maxRetries, int timeout, List<int> backoffMs)
        {
            _sessionProvider = sessionProvider;
            Endpoint = url;
            MaxRetries = maxRetries;
            Timeout = timeout;
            ExponentialBackoffMs = backoffMs;
        }
        
        public string GetEndpoint()
        {
            if (Utility.UrlFormatter.TryResolveUrl(_sessionProvider , Endpoint, out var resolvedUrl))
            {
                return resolvedUrl;
            }

            Debug.LogError($"Failed to resolve endpoint URL: {Endpoint}");
            return string.Empty;
        }
        
        public void IncrementExponentialBackoff()
        {
            _exponentialBackoffIndex++;
        }
        
        public int GetExponentialBackoffMs()
        {
            if (ExponentialBackoffMs == null || ExponentialBackoffMs.Count == 0)
            {
                return 5000; // Default value if the list is empty
            }

            var index = Math.Min(_exponentialBackoffIndex, ExponentialBackoffMs.Count - 1);
            return ExponentialBackoffMs[index];
        }
    }
}