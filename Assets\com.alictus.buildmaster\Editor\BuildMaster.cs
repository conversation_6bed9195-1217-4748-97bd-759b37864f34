﻿using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using com.alictus.buildmaster.Editor.Utilities;

[Obsolete("Deprecated. Use com.alictus.buildmaster.BuildMaster instead")]
public static class MyEditorScriptWithParams
{
    public static void TestScript() => com.alictus.buildmaster.BuildMaster.BuildIOS();
    public static void TestAndroidScript() => com.alictus.buildmaster.BuildMaster.BuildAndroid();
    public static void HandleManifest() => com.alictus.buildmaster.BuildMaster.DummyMethod();
    public static void SwitchToAlictusInternalModule() => com.alictus.buildmaster.BuildMaster.SwitchToAlictusInternalModule();
}

namespace com.alictus.buildmaster
{
    [Obsolete("Deprecated. Use com.alictus.buildmaster.BuildMaster instead")]
    public static class BuildManager
    {
        public static void BuildIOS() => com.alictus.buildmaster.BuildMaster.BuildIOS();
        public static void TestScript() => com.alictus.buildmaster.BuildMaster.BuildIOS();
        public static void TestAndroidScript() => com.alictus.buildmaster.BuildMaster.BuildAndroid();
        public static void HandleManifest() => com.alictus.buildmaster.BuildMaster.DummyMethod();
        public static void SwitchToAlictusInternalModule() => com.alictus.buildmaster.BuildMaster.SwitchToAlictusInternalModule();
    }
}

namespace com.alictus.buildmaster
{
    public static class BuildMaster
    {
        public static event Action OnPreBuild;
        public static event Action OnPostBuild;

        public static void BuildIOSToPath(string outputPath)
        {
            try
            {
                EarlyBuildCallback();

                var options = CreateBuildOptions(outputPath, BuildTarget.iOS, BuildTargetGroup.iOS);

                BuildPipeline.BuildPlayer(options);

                LateBuildCallback();
            }
            catch (Exception e)
            {
                BuildLogger.LogError($"Exception raised {e}");
                if (Application.isBatchMode)
                {
                    Application.Quit(1);
                }
            }
        }
        public static void BuildAndroidToPath(string outputPath)
        {
            try
            {
                EarlyBuildCallback();
                var options = CreateBuildOptions(outputPath, BuildTarget.Android, BuildTargetGroup.Android);

                EditorUserBuildSettings.exportAsGoogleAndroidProject = true;

                BuildPipeline.BuildPlayer(options);
                LateBuildCallback();
            }
            catch (Exception e)
            {
                BuildLogger.LogError($"Exception raised {e}");
                if (Application.isBatchMode)
                {
                    Application.Quit(1);
                }
            }
        }

        private static BuildOptions ParseBuildOptions(string stringOptions)
        {
            var options = BuildOptions.CompressWithLz4HC;
            if (!string.IsNullOrEmpty(stringOptions))
            {
                BuildLogger.LogInfo($"Development mode BuildOptions string: {stringOptions}");
                if (stringOptions.Contains("+WaitForPlayerConnection", StringComparison.CurrentCultureIgnoreCase))
                {
                    options |= BuildOptions.WaitForPlayerConnection;
                    BuildLogger.LogInfo($"Enabled: {BuildOptions.WaitForPlayerConnection}");
                }
                if (stringOptions.Contains("+EnableDeepProfilingSupport", StringComparison.CurrentCultureIgnoreCase))
                {
                    options |= BuildOptions.EnableDeepProfilingSupport;
                    BuildLogger.LogInfo($"Enabled: {BuildOptions.EnableDeepProfilingSupport}");
                }
                if (stringOptions.Contains("+AllowDebugging", StringComparison.CurrentCultureIgnoreCase))
                {
                    options |= BuildOptions.AllowDebugging;
                    BuildLogger.LogInfo($"Enabled: {BuildOptions.AllowDebugging}");
                }
                if (stringOptions.Contains("+ConnectWithProfiler", StringComparison.CurrentCultureIgnoreCase))
                {
                    options |= BuildOptions.ConnectWithProfiler;
                    BuildLogger.LogInfo($"Enabled: {BuildOptions.ConnectWithProfiler}");
                }
                if (stringOptions.Contains("+Development", StringComparison.CurrentCultureIgnoreCase))
                {
                    options |= BuildOptions.Development;
                    BuildLogger.LogInfo($"Enabled: {BuildOptions.Development}");
                }
            }
            return options;
        }
        private static BuildPlayerOptions CreateBuildOptions(string outputPath, BuildTarget target, BuildTargetGroup targetGroup)
        {
            var stringOptions = Environment.GetEnvironmentVariable("BUILDMASTER_BUILD_OPTIONS", EnvironmentVariableTarget.Process);

            var options = ParseBuildOptions(stringOptions);

            return new BuildPlayerOptions() {
                scenes = FindEnabledEditorScenes(),
                locationPathName = outputPath,
                target = target,
                targetGroup = targetGroup,
                options = options
            };
        }

        public static void BuildIOS() => BuildIOSToPath(BuildConstants.OUTPUT_DEFAULT_PATH);
        public static void BuildAndroid() => BuildAndroidToPath(BuildConstants.OUTPUT_DEFAULT_PATH);

        [MenuItem(BuildConstants.MENU_ITEM_ROOT + "Build With Custom Path")]
        private static void BuildWithCustomPath()
        {
            var path = EditorUtility.OpenFolderPanel("Output Directory", string.Empty, null);
            if (string.IsNullOrEmpty(path)) return;

            path = Path.Combine(path, Application.identifier.Replace(".", "_"));

#if UNITY_IOS
            BuildIOSToPath(path);
#elif UNITY_ANDROID
            BuildAndroidToPath(path);
#endif
        }

        public static void DummyMethod()
        {
            BuildTargetGroup[] targetGroups = new[] {
                BuildTargetGroup.Android, BuildTargetGroup.iOS
            };

            foreach (BuildTargetGroup buildTargetGroup in targetGroups)
            {
                List<string> symbols = new List<string>(PlayerSettings
                    .GetScriptingDefineSymbolsForGroup(buildTargetGroup).Split(';'));

                if (!symbols.Contains("DUMMY_DEF_SYMBOL"))
                {
                    symbols.Add("DUMMY_DEF_SYMBOL");
                }
                else
                {
                    symbols.Remove("DUMMY_DEF_SYMBOL");
                }

                PlayerSettings.SetScriptingDefineSymbolsForGroup(buildTargetGroup, string.Join(";", symbols));
            }
        }

        public static void SwitchToAlictusInternalModule()
        {
            BuildTargetGroup[] targetGroups = new[] {
                BuildTargetGroup.Android, BuildTargetGroup.iOS
            };

            foreach (BuildTargetGroup buildTargetGroup in targetGroups)
            {
                List<string> symbols = new List<string>(PlayerSettings
                    .GetScriptingDefineSymbolsForGroup(buildTargetGroup).Split(';'));

                if (!symbols.Contains("ALICTUS_INTERNAL_MODULE"))
                {
                    symbols.Add("ALICTUS_INTERNAL_MODULE");
                }

                PlayerSettings.SetScriptingDefineSymbolsForGroup(buildTargetGroup, string.Join(";", symbols));
            }
        }

        private static void EarlyBuildCallback()
        {
            Editor.BuildJobs.ChangeAndroidTargetSDK.Execute();

            var databaseBuildNumber = BuildBackendUtilities.GetBuildNumber();
            BuildLogger.LogInfo($"Build backend buildnumber: {databaseBuildNumber}");
            ProjectUtilities.SetProjectBuildNumber($"{databaseBuildNumber}");

            BuildLogger.LogInfo($"Overriding project version from file");
            var projectVersion = ProjectUtilities.GetLocalProjectVersion();
            BuildLogger.LogInfo($"Project version defined in file: {projectVersion}");
            ProjectUtilities.SetProjectVersion(projectVersion);
            ProjectUtilities.SaveToLocal();
            BuildLogger.LogInfo($"Version set to: {projectVersion}");

            OnPreBuild?.Invoke();
        }
        private static void LateBuildCallback()
        {
            OnPostBuild?.Invoke();
        }

        private static string[] FindEnabledEditorScenes()
        {
            List<string> EditorScenes = new List<string>();
            foreach (EditorBuildSettingsScene scene in EditorBuildSettings.scenes)
            {
                if (!scene.enabled) continue;
                EditorScenes.Add(scene.path);
            }
            return EditorScenes.ToArray();
        }
    }
}