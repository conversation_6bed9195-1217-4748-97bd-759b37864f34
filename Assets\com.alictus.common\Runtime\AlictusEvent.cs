﻿namespace com.alictus.common
{
    public static class AlictusEvent
    {
        
        public const string ON_SDK_READY = "OnSDKReady";

        public static class Unity
        {
            private const string PREFIX = "Unity_";
            public const string ON_AWAKE = PREFIX + "OnAwake";
            public const string ON_START = PREFIX + "OnStart";
            public const string ON_UPDATE = PREFIX + "OnUpdate";
            public const string ON_LATE_UPDATE = PREFIX + "OnLateUpdate";
            public const string ON_APPLICATION_RESUME = PREFIX + "OnApplicationResume";
            public const string ON_APPLICATION_FOCUS_TRUE = PREFIX + "OnApplicationFocusTrue";
            public const string ON_APPLICATION_FOCUS_FALSE = PREFIX + "OnApplicationFocusFalse";
            public const string ON_APPLICATION_PAUSE_TRUE = PREFIX + "OnApplicationPauseTrue";
            public const string ON_APPLICATION_PAUSE_FALSE = PREFIX + "OnApplicationPauseFalse";
            public const string ON_APPLICATION_QUIT = PREFIX + "OnApplicationQuit";
            
            public static class CoroutineRunner
            {
                private const string PREFIX = Unity.PREFIX + "CoroutineRunner_";
                public const string ON_COROUTINE_RUNNER_READY = PREFIX + "OnReady";
            }
        }

        public static class IPLocation
        {
            private const string PREFIX = "IPLocation_";
            internal const string ON_OBTAINED = PREFIX + "OnObtained";
        }
        
        public static class AppRating
        {
            private const string PREFIX = "AppRating_";
            internal const string ON_RATE = PREFIX + "OnRateEvent";
        }
        
        internal static class InternalAnalytics
        {
            private const string PREFIX = "InternalAnalytic_";
            
            internal const string AD_STATUS = PREFIX + "OnAdStatusEvent";
            internal const string AD_ERROR = PREFIX + "OnAdErrorEvent";
            
            internal const string AD_REW_START = PREFIX + "OnAdRewStart";
            
            internal const string AD_REW_OPP = PREFIX + "OnAdRewOPP";
            
            internal const string ROAS = PREFIX + "OnROASUpdateEvent";
            
            internal const string USER_PROPERTY_UPDATED = PREFIX + "UserPropertyUpdated";
            internal const string EVENT_OCCURED = PREFIX + "EventOccured";
            internal const string EVENT_OCCURED_FOR_PLATFORM = PREFIX + "EventOccuredForPlatform_";
            internal const string EVENT_OCCURED_FOR_ALL_PLATFORMS = PREFIX + "EventOccuredForAllPlatforms";
        }
        public static class AppTracking
        {
            private const string PREFIX = "AppTracking_";
            public const string RESULTED = PREFIX + "ATTResulted";
        }
        
        internal static class InternalAppTracking
        {
            private const string PREFIX = "InternalAppTracking_";
            internal const string APP_TRACKER_ATT_RESULTED = PREFIX + "ATTResulted";
            internal const string APP_TRACKER_INITIALIZED = PREFIX + "AppTrackerInitialized";
            internal const string APP_TRACKER_CUSTOM_EVENT = PREFIX + "AppTrackerCustomEvent";
            internal const string APP_TRACKER_IAP_EVENT = PREFIX + "AppTrackerIAPEvent";
            internal const string APP_TRACKER_REVENUE_EVENT = PREFIX + "AppTrackerRevenueEvent";
        }
        
        public static class Analytics
        {
            private const string PREFIX = "Analytics_";
            public const string ANALYTICS_INITIALIZED = PREFIX + "ANALYTICS_INITIALIZED";
            public const string ADD_GLOBAL_CUSTOM_EVENT_FIELD = PREFIX + "ADD_GLOBAL_CUSTOM_EVENT_FIELD";
        }

        public static class RemoteConfig
        {
            private const string PREFIX = "RConf_";
            public const string FETCHING_STARTED = PREFIX + "FetchingStarted";
            public const string FETCHING_COMPLETED = PREFIX + "FetchingCompleted";
            public const string USER_AB_TESTS_DEACTIVATED = PREFIX + "UserAbTestsDeactivated";
            public const string USER_AB_TESTS_ASSIGNED = PREFIX + "UserAbTestsAssigned";
            public const string ON_FETCHING_DELAYED = PREFIX + "FetchingDelayed";
            public const string ON_FETCHING_COMPLETED_IN_TIME = PREFIX + "FetchingCompletedInTime";
        }

        internal static class QualityTracking
        {
            private const string PREFIX = "QT_";
            internal const string APP_READY_TO_INTERACT = PREFIX + "AppReadyToInteract";
        }
        
        internal static class LevelTracking
        {
            private const string PREFIX = "LT_";
            internal const string BEGIN_TRACKING = PREFIX + "BeginTracking";
            internal const string FIRST_TOUCH = PREFIX + "FirstTouch";
            internal const string END_TRACKING = PREFIX + "EndTracking";
        }
        
        public static class Consent
        {
            private const string PREFIX = "CONSENT_";
            public const string PRIVACY_POLICY_OPENED = PREFIX + "PP_OPEN";
            public const string ACCEPTED = PREFIX + "ACCEPTED";
            public const string SHOWN = PREFIX + "SHOWN";
        }
        
        public static class ThirdParties
        {
            public static class Firebase
            {
                private const string PREFIX = "FBase_";
                public const string INITIALIZED = PREFIX + "Initialized";
                internal const string INITIALIZATION_ERROR = PREFIX + "InitializationError";
                //internal const string REMOTE_CONFIG_FECTHING_STARTED = PREFIX + "RemoteConfigInitializationStarted";
                //internal const string REMOTE_CONFIG_INITIALIZED = PREFIX + "RemoteConfigInitialized";
                internal const string ANALYTICS_INITIALIZED = PREFIX + "AnalyticsInitialized";
            }
            
            public static class GameAnalytics
            {
                private const string PREFIX = "GAMEA_";
                public const string INITIALIZE_CALLED = PREFIX + "InitializeCalled";
                internal const string REMOTE_CONFIG_INITIALIZED = PREFIX + "RemoteConfigInitialized";
            }
        }

        public static class IAPHandling
        {
            private const string PREFIX = "IAP_";
            public const string INITIALIZE_FINALIZED = PREFIX + "InitializeFinalized";
        }
        public static class BackgroundHandling
        {
            private const string PREFIX = "BGHANDLE_";
            public const string APP_WENT_BACKGROUND = PREFIX + "OnBackground";
            public const string APP_CAME_FOREGROUND = PREFIX + "OnForeground";
        }

        #if UNITY_EDITOR
        internal static class Editor
        {
            private const string PREFIX = "Editor_";
            internal const string CONFIG_CHANGED = PREFIX + "ConfigChanged";
            internal const string SDK_UPDATE_CALLED = PREFIX + "SdkUpdateCalled";
        }

        #endif

    }
}
