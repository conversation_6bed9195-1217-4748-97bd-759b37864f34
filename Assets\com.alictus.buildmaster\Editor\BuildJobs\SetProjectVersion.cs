using System;
using System.IO;
using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEngine;
using com.alictus.buildmaster.Editor.Utilities;

namespace com.alictus.buildmaster.Editor.BuildJobs
{
    public class SetProjectVersion : IPreprocessBuildWithReport
    {
        public int callbackOrder => (int)PreBuildJobType.SetProjectVersion;
        public void OnPreprocessBuild(BuildReport report) => Execute();

        public static void Execute()
        {
            BuildLogger.LogInfo("Setting Version");
            try
            { 
                var localVersion = ProjectUtilities.GetLocalProjectVersion(); 
                ProjectUtilities.SetProjectVersion(localVersion);
            }
            catch (Exception e)
            {
                BuildLogger.LogError($"Version could not be changed.\n{e.Message}");
                throw;
            }
        }

        private static bool IsVersionGreaterThanOrEqual(string lhs, string rhs)
        {
            string[] lhsVersionNumbers = lhs.Split('.');
            string[] rhsVersionNumbers = rhs.Split('.');

            int comparisionRange = Mathf.Min(lhsVersionNumbers.Length, rhsVersionNumbers.Length);

            for (var i = 0; i < comparisionRange; i++)
            {
                if (int.Parse(lhsVersionNumbers[i]) > int.Parse(rhsVersionNumbers[i]))
                    return true;
                if (int.Parse(lhsVersionNumbers[i]) < int.Parse(rhsVersionNumbers[i]))
                    return false;
            }

            return lhsVersionNumbers.Length >= rhsVersionNumbers.Length;
        }
    }
}